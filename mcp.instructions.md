# 🔌 Model Context Protocol (MCP) Instructions

Copilot evaluates these rules silently on every request.

Available Servers
| Server ID         | Purpose                                                      |
|-------------------|--------------------------------------------------------------|
| discord-mcp       | Interact with Discord API (events, messages, channels).      |
| context7-mcp      | AI fact-checking and source retrieval.                       |
| cache-mcp         | Cache fact-check results and manage rate limits.             |
| toolbox-mcp       | Discover and route to additional MCP tools.                  |
| sequential-think  | Chain-of-thought planning and subtask decomposition.         |
| shrimp-task       | Manage and track task execution flows.                       |
| code-review-mcp   | Analyze and suggest improvements in generated code.          |
| vibe-check-mcp    | Align responses with user tone and intent.                   |

Selection Heuristics
- Discord integration? Use discord-mcp.
- Need AI verification? Use context7-mcp.
- Prevent duplicate calls? Use cache-mcp.
- Unsure tool choice? Ask toolbox-mcp.
- Multi-step planning? Use sequential-think.
- Code quality feedback? Use code-review-mcp.
- Match user vibe? Use vibe-check-mcp.

Invocation Pattern
mcp run "<server>" --tool "<tool>" --args "…" --output json

Fallback
If no server matches, continue with local reasoning but log the limitation.
