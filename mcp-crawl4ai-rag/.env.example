# The transport for the MCP server - either 'sse' or 'stdio' (defaults to sse if left empty)
TRANSPORT=

# Host to bind to if using sse as the transport (leave empty if using stdio)
# Set this to 0.0.0.0 if using Docker, otherwise set to localhost (if using uv)
HOST=

# Port to listen on if using sse as the transport (leave empty if using stdio)
PORT=

# Get your Open AI API Key by following these instructions -
# https://help.openai.com/en/articles/4936850-where-do-i-find-my-openai-api-key
# This is for the embedding model - text-embed-small-3 will be used
OPENAI_API_KEY=

# The LLM you want to use for summaries and contextual embeddings
# Generally this is a very cheap and fast LLM like gpt-4.1-nano
MODEL_CHOICE=

# RAG strategies - set these to "true" or "false" (default to "false")
# USE_CONTEXTUAL_EMBEDDINGS: Enhances embeddings with contextual information for better retrieval
USE_CONTEXTUAL_EMBEDDINGS=false

# USE_HYBRID_SEARCH: Combines vector similarity search with keyword search for better results
USE_HYBRID_SEARCH=false

# USE_AGENTIC_RAG: Enables code example extraction, storage, and specialized code search functionality
USE_AGENTIC_RAG=false

# USE_RERANKING: Applies cross-encoder reranking to improve search result relevance
USE_RERANKING=false

# USE_KNOWLEDGE_GRAPH: Enables AI hallucination detection and repository parsing tools using Neo4j
# If you set this to true, you must also set the Neo4j environment variables below.
USE_KNOWLEDGE_GRAPH=false

# For the Supabase version (sample_supabase_agent.py), set your Supabase URL and Service Key.
# Get your SUPABASE_URL from the API section of your Supabase project settings -
# https://supabase.com/dashboard/project/<your project ID>/settings/api
SUPABASE_URL=

# Get your SUPABASE_SERVICE_KEY from the API section of your Supabase project settings -
# https://supabase.com/dashboard/project/<your project ID>/settings/api
# On this page it is called the service_role secret.
SUPABASE_SERVICE_KEY=

# Neo4j Configuration for Knowledge Graph Tools
# These are required for the AI hallucination detection and repository parsing tools
# Leave empty to disable knowledge graph functionality

# Neo4j connection URI - use bolt://localhost:7687 for local, neo4j:// for cloud instances
# IMPORTANT: If running the MCP server through Docker, change localhost to host.docker.internal
NEO4J_URI=bolt://localhost:7687

# Neo4j username (usually 'neo4j' for default installations)
NEO4J_USER=neo4j

# Neo4j password for your database instance
NEO4J_PASSWORD=