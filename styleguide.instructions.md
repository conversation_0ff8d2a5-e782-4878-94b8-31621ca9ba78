# 🎨 Styleguide Instructions: Discord AI Fact-Checker Bot

### Formatting & Linting
- TypeScript/JavaScript/React – Prettier + ESLint (Airbnb + TypeScript).
- YAML/JSON – Prettier.

### Code Conventions
- Folder Structure: `src/commands`, `src/events`, `src/mcp`, `src/utils`, `tests/`.
- Identifiers: camelCase for variables/functions, PascalCase for classes/types.
- Constants: UPPER_SNAKE_CASE.
- Async: prefer `async/await`; handle errors with `try/catch`.
- Dependency injection for MCP adapters.

### Build & Test
- TypeScript compile: `tsc --noEmit` with strict settings.
- Tests: `npm test` (Vitest) with Discord and Context7 mocks.

### Documentation
- Use TSDoc for functions, classes, and public interfaces.
- Provide JSDoc on commands and event handlers.

### Accessibility & Style
- Discord messages: use embeds with clear titles and fields.
- Errors: user-friendly error messages, no stack traces in chat.

### Git & Commits
- Main branch as source of truth.
- Feature branches per command/feature.
- Conventional Commits.
