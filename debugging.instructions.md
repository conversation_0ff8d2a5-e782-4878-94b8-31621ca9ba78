# 🐞 Debugging Instructions: Discord AI Fact-Checker Bot

Workflow
- Reproduce → Isolate → Inspect → Fix & Verify.

Steps
- Reproduce – simulate Discord events and bot responses deterministically.
- Isolate – use feature flags or mock MCP calls (discord-mcp, context7-mcp).
- Inspect – attach VS Code debugger to Node.js process; set breakpoints in event handlers.
- Log – use structured logs (e.g., `winston`) and inspect via Debug Console.
- Fix & Verify – write a test reproducing the bug, implement the fix, rerun tests.

Tips
- Enable source maps for clearer stack traces.
- For async flakiness, replace `setTimeout` with `await` and proper Promises.
- Capture CPU & memory profiles with `node --inspect` and Chrome DevTools.
