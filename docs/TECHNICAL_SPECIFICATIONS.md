# 🔧 Technical Specifications - Discord AI Fact-Checker Bot

## System Architecture

### Core Components
```
┌─────────────────────────────────────────────────────────────┐
│                    Discord AI Fact-Checker Bot             │
├─────────────────────────────────────────────────────────────┤
│  Web Dashboard (React + TypeScript)                        │
│  ├── Configuration Management                              │
│  ├── Real-time Monitoring                                  │
│  ├── Log Viewer & Analytics                                │
│  └── User Management                                       │
├─────────────────────────────────────────────────────────────┤
│  Bot Core (Node.js + TypeScript)                          │
│  ├── Discord.js v14+ Integration                          │
│  ├── Command Handler & Event System                       │
│  ├── Rate Limiting & Queue Management                     │
│  └── Context Management                                   │
├─────────────────────────────────────────────────────────────┤
│  AI Inference Engine (Python + FastAPI)                   │
│  ├── llama.cpp Integration                                │
│  ├── Qwen2.5-4B-Instruct Model                           │
│  ├── GPU Acceleration (ROCm/CUDA)                        │
│  └── Context Window Management (32K)                      │
├─────────────────────────────────────────────────────────────┤
│  Web Crawling Service (Python)                            │
│  ├── Crawl4AI Integration                                 │
│  ├── Source Verification                                  │
│  ├── Content Extraction & Summarization                  │
│  └── Cache Management                                     │
├─────────────────────────────────────────────────────────────┤
│  Data Layer                                               │
│  ├── SQLite Database (Local Storage)                     │
│  ├── Vector Database (ChromaDB)                          │
│  ├── Cache Layer (Redis-compatible)                      │
│  └── Configuration Store                                 │
└─────────────────────────────────────────────────────────────┘
```

## Hardware Requirements

### Minimum Requirements
- **CPU**: 4-core processor (Intel i5-8400 / AMD Ryzen 5 2600)
- **RAM**: 8GB system memory
- **GPU**: 4GB VRAM (GTX 1660 / RX 580 or better)
- **Storage**: 10GB free space
- **Network**: Stable internet connection

### Recommended Requirements
- **CPU**: 6+ core processor (Intel i7-10700K / AMD Ryzen 7 3700X)
- **RAM**: 16GB system memory
- **GPU**: 8GB+ VRAM (RTX 3070 / RX 6700 XT or better)
- **Storage**: 20GB free space (SSD recommended)
- **Network**: High-speed internet for web crawling

### Target Hardware Compatibility
- **AMD Build**: Ryzen 7 7800X3D + Radeon RX 7900 GRE (20GB VRAM)
- **NVIDIA Build**: Intel i5-12600K + RTX 2070 (8GB VRAM)

## Software Stack

### Core Technologies
- **Runtime**: Node.js 18+ (Bot Core), Python 3.11+ (AI Engine)
- **AI Framework**: llama.cpp with GPU acceleration
- **Discord Library**: discord.js v14+
- **Web Framework**: React 18+ with TypeScript
- **Database**: SQLite + ChromaDB for vectors
- **Containerization**: Docker (optional deployment)

### AI Model Specifications
- **Model**: Qwen2.5-4B-Instruct
- **Context Window**: 32,768 tokens
- **VRAM Usage**: ~2.5GB (4-bit quantization)
- **Inference Speed**: ~50-100 tokens/second (GPU)
- **Model Size**: ~2.4GB on disk

### GPU Acceleration
- **AMD**: ROCm 5.7+ support
- **NVIDIA**: CUDA 11.8+ support
- **Fallback**: CPU inference with OpenBLAS
- **Memory Management**: Dynamic VRAM allocation

## Network Architecture

### Internal Communication
```
Web Dashboard (Port 3000) ←→ Bot Core (Port 3001)
                                    ↓
Bot Core ←→ AI Engine (Port 8000) ←→ llama.cpp
    ↓
Discord API (WebSocket + REST)
    ↓
Web Crawling Service (Port 8001)
```

### External Dependencies
- **Discord API**: WebSocket + REST endpoints
- **Web Crawling**: HTTP/HTTPS requests to target sites
- **Model Downloads**: Hugging Face Hub (initial setup)
- **Updates**: GitHub Releases API

## Security Architecture

### Data Protection
- **Local Processing**: All AI inference happens locally
- **Encrypted Storage**: SQLite database encryption
- **Secure Communication**: TLS for all external connections
- **Token Management**: Encrypted Discord bot token storage

### Privacy Features
- **No Data Collection**: No telemetry or usage tracking
- **Local Logs**: All logs stored locally only
- **Configurable Retention**: User-defined log retention periods
- **GDPR Compliant**: No personal data transmission

## Performance Specifications

### Response Times
- **Simple Fact-Check**: <2 seconds
- **Complex Analysis**: <5 seconds
- **Web Crawling**: <10 seconds
- **Context Loading**: <1 second

### Throughput
- **Concurrent Users**: 50+ simultaneous Discord users
- **Messages/Minute**: 100+ fact-checks per minute
- **Cache Hit Rate**: >80% for repeated queries
- **Uptime Target**: 99.5%

### Resource Usage
- **CPU Usage**: <20% during idle, <60% during peak
- **RAM Usage**: ~4GB base, +2GB per active session
- **VRAM Usage**: 2.5GB base model, +500MB per concurrent request
- **Disk I/O**: <100MB/hour logging and caching

## Deployment Architecture

### Windows Deployment
- **Installer**: NSIS-based .exe installer
- **Service**: Windows Service integration
- **Auto-Start**: System startup integration
- **Updates**: Built-in updater with rollback

### Arch Linux Deployment
- **Package**: AUR package with dependencies
- **Service**: systemd service integration
- **Configuration**: /etc/discord-factchecker/
- **Updates**: pacman integration

### Cross-Platform Components
- **Configuration**: YAML-based configuration files
- **Logging**: Structured JSON logs with rotation
- **Monitoring**: Health check endpoints
- **Backup**: Automated database backups
