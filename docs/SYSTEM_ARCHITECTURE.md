# 🏗️ System Architecture - Discord AI Fact-Checker Bot

## Overview
The Discord AI Fact-Checker Bot is designed as a modular, microservices-based architecture optimized for local deployment with GPU acceleration. The system prioritizes performance, privacy, and ease of use.

## Component Architecture

### 1. Bot Core Service (Node.js/TypeScript)
**Purpose**: Discord integration and command handling
**Port**: 3001
**Key Responsibilities**:
- Discord WebSocket connection management
- Slash command registration and handling
- Message event processing
- Rate limiting and queue management
- User session management
- Configuration hot-reloading

**Key Files**:
```
src/bot/
├── index.ts                 # Main bot entry point
├── commands/               # Slash command handlers
│   ├── factcheck.ts        # Manual fact-check command
│   ├── configure.ts        # Bot configuration
│   └── stats.ts           # Usage statistics
├── events/                # Discord event handlers
│   ├── messageCreate.ts   # Auto fact-checking
│   ├── interactionCreate.ts # Command interactions
│   └── ready.ts          # Bot startup
├── services/              # Core services
│   ├── aiService.ts       # AI engine communication
│   ├── crawlService.ts    # Web crawling integration
│   └── cacheService.ts    # Local caching
└── utils/                 # Utility functions
    ├── rateLimiter.ts     # Discord rate limiting
    ├── contextManager.ts  # Conversation context
    └── logger.ts          # Structured logging
```

### 2. AI Inference Engine (Python/FastAPI)
**Purpose**: Local AI model inference with GPU acceleration
**Port**: 8000
**Key Responsibilities**:
- Qwen2.5-4B model loading and inference
- GPU memory management
- Context window optimization
- Batch processing for efficiency
- Model quantization and optimization

**Key Files**:
```
src/ai/
├── main.py                # FastAPI application
├── models/               # Model management
│   ├── qwen_handler.py   # Qwen model wrapper
│   ├── gpu_manager.py    # GPU acceleration
│   └── quantization.py   # Model optimization
├── inference/            # Inference logic
│   ├── fact_checker.py   # Fact-checking pipeline
│   ├── context_manager.py # Context handling
│   └── batch_processor.py # Batch inference
└── utils/               # Utilities
    ├── llama_cpp_wrapper.py # llama.cpp integration
    ├── memory_manager.py    # VRAM management
    └── performance_monitor.py # Performance tracking
```

### 3. Web Crawling Service (Python)
**Purpose**: Intelligent web content extraction and verification
**Port**: 8001
**Key Responsibilities**:
- Website content extraction
- Source credibility assessment
- Content summarization
- Cache management
- Rate limiting for external requests

**Key Files**:
```
src/crawler/
├── main.py              # Crawling service entry
├── crawlers/           # Specialized crawlers
│   ├── news_crawler.py # News site handling
│   ├── wiki_crawler.py # Wikipedia integration
│   └── generic_crawler.py # General web crawling
├── extractors/         # Content extraction
│   ├── text_extractor.py # Clean text extraction
│   ├── fact_extractor.py # Fact identification
│   └── source_validator.py # Source verification
└── cache/             # Caching system
    ├── content_cache.py # Content caching
    └── source_cache.py  # Source metadata cache
```

### 4. Web Dashboard (React/TypeScript)
**Purpose**: Professional management interface
**Port**: 3000
**Key Responsibilities**:
- Real-time bot monitoring
- Configuration management
- Log viewing and analysis
- User management
- Performance analytics

**Key Files**:
```
src/dashboard/
├── src/
│   ├── components/      # React components
│   │   ├── Dashboard.tsx # Main dashboard
│   │   ├── ConfigPanel.tsx # Configuration
│   │   ├── LogViewer.tsx # Log management
│   │   └── Analytics.tsx # Performance metrics
│   ├── services/       # API services
│   │   ├── botApi.ts   # Bot API integration
│   │   ├── configApi.ts # Configuration API
│   │   └── logsApi.ts  # Logging API
│   └── utils/         # Utilities
│       ├── websocket.ts # Real-time updates
│       └── formatters.ts # Data formatting
├── public/            # Static assets
└── package.json       # Dependencies
```

## Data Flow Architecture

### Fact-Checking Pipeline
```
Discord Message → Bot Core → Context Analysis → AI Engine
                     ↓              ↓              ↓
                Queue Manager → Web Crawler → Fact Verification
                     ↓              ↓              ↓
                Response Format ← Source Validation ← Confidence Score
                     ↓
                Discord Response
```

### Configuration Management
```
Web Dashboard → Configuration API → Bot Core
                      ↓               ↓
                Config Validation → Hot Reload
                      ↓               ↓
                Database Update → Service Restart (if needed)
```

## Database Schema

### SQLite Tables
```sql
-- Bot configuration
CREATE TABLE config (
    key TEXT PRIMARY KEY,
    value TEXT NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Fact-checking history
CREATE TABLE fact_checks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    discord_user_id TEXT NOT NULL,
    discord_message_id TEXT NOT NULL,
    original_statement TEXT NOT NULL,
    fact_check_result TEXT NOT NULL,
    confidence_score REAL NOT NULL,
    sources TEXT NOT NULL, -- JSON array
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Web crawling cache
CREATE TABLE crawl_cache (
    url_hash TEXT PRIMARY KEY,
    url TEXT NOT NULL,
    content TEXT NOT NULL,
    metadata TEXT NOT NULL, -- JSON
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Performance metrics
CREATE TABLE performance_metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    metric_name TEXT NOT NULL,
    metric_value REAL NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### ChromaDB Collections
```python
# Fact database for semantic search
fact_collection = {
    "name": "facts",
    "metadata": {
        "description": "Verified facts with sources",
        "embedding_model": "sentence-transformers/all-MiniLM-L6-v2"
    }
}

# Source credibility database
source_collection = {
    "name": "sources",
    "metadata": {
        "description": "Source credibility and metadata",
        "embedding_model": "sentence-transformers/all-MiniLM-L6-v2"
    }
}
```

## Inter-Service Communication

### API Endpoints

#### Bot Core → AI Engine
```typescript
POST /api/v1/fact-check
{
    "statement": string,
    "context": string[],
    "user_id": string,
    "priority": "low" | "normal" | "high"
}

Response:
{
    "result": string,
    "confidence": number,
    "reasoning": string,
    "processing_time": number
}
```

#### Bot Core → Web Crawler
```typescript
POST /api/v1/crawl
{
    "urls": string[],
    "max_depth": number,
    "timeout": number
}

Response:
{
    "results": Array<{
        "url": string,
        "content": string,
        "metadata": object,
        "credibility_score": number
    }>
}
```

#### Dashboard → Bot Core
```typescript
GET /api/v1/status
Response:
{
    "status": "online" | "offline" | "maintenance",
    "uptime": number,
    "active_sessions": number,
    "queue_length": number,
    "performance_metrics": object
}

POST /api/v1/config
{
    "key": string,
    "value": any
}
```

## Security Architecture

### Authentication & Authorization
- **Dashboard Access**: Local-only by default, optional password protection
- **API Security**: Internal service authentication via shared secrets
- **Discord Integration**: Secure token storage with encryption at rest

### Data Protection
- **Encryption**: AES-256 for sensitive configuration data
- **Local Processing**: All AI inference happens locally
- **Privacy**: No external data transmission except Discord API

### Network Security
- **Firewall Rules**: Only necessary ports exposed
- **TLS**: All external communications encrypted
- **Rate Limiting**: Protection against abuse and DoS

## Performance Optimization

### GPU Memory Management
```python
class GPUMemoryManager:
    def __init__(self, max_vram_usage=0.7):
        self.max_vram_usage = max_vram_usage
        self.current_usage = 0
        
    def allocate_memory(self, required_mb):
        # Dynamic allocation with gaming-friendly limits
        available = self.get_available_vram()
        if required_mb > available * self.max_vram_usage:
            return self.fallback_to_cpu()
        return self.allocate_gpu_memory(required_mb)
```

### Caching Strategy
- **L1 Cache**: In-memory response cache (1 hour TTL)
- **L2 Cache**: SQLite database cache (24 hour TTL)
- **L3 Cache**: Web crawling cache (7 day TTL)
- **Cache Invalidation**: Smart invalidation based on content freshness

### Batch Processing
- **Request Batching**: Group similar requests for efficient processing
- **Context Sharing**: Reuse context across related fact-checks
- **Parallel Processing**: Multi-threaded crawling and inference
