# 🎨 Web Dashboard Design - Discord AI Fact-Checker Bot

## Design Philosophy
The web dashboard follows modern design principles with a focus on:
- **Professional Appearance**: Clean, corporate-grade interface
- **Usability**: Intuitive navigation and clear information hierarchy
- **Real-time Updates**: Live data with WebSocket connections
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Dark/Light Themes**: User preference support
- **Accessibility**: WCAG 2.1 AA compliance

## Dashboard Layout

### Main Navigation
```
┌─────────────────────────────────────────────────────────────┐
│ 🤖 Discord FactChecker    [🌙] [⚙️] [👤] [🔔] [❓]        │
├─────────────────────────────────────────────────────────────┤
│ 📊 Dashboard │ ⚙️ Config │ 📋 Logs │ 📈 Analytics │ 👥 Users │
└─────────────────────────────────────────────────────────────┘
```

### Dashboard Overview Page
```
┌─────────────────────────────────────────────────────────────┐
│                        System Status                        │
├─────────────────────────────────────────────────────────────┤
│ 🟢 Bot Online    🟢 AI Engine    🟢 Web Crawler    🟡 GPU   │
│ Uptime: 2d 14h  Response: 1.2s  Cache Hit: 87%   VRAM: 65% │
├─────────────────────────────────────────────────────────────┤
│                     Quick Statistics                        │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│ │Fact Checks  │ │Active Users │ │Success Rate │ │Avg Time │ │
│ │    1,247    │ │     23      │ │   94.2%     │ │  1.8s   │ │
│ │   ↑ +12%    │ │   ↑ +3      │ │   ↑ +2.1%   │ │ ↓ -0.3s │ │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    Recent Activity                          │
├─────────────────────────────────────────────────────────────┤
│ 🔍 User#1234 fact-checked "Climate change statistics"      │
│ ✅ Verified with 92% confidence • 2 sources • 1.4s        │
│                                                    2m ago   │
│ ─────────────────────────────────────────────────────────── │
│ 🔍 User#5678 fact-checked "COVID vaccine effectiveness"    │
│ ✅ Verified with 88% confidence • 4 sources • 2.1s        │
│                                                    5m ago   │
└─────────────────────────────────────────────────────────────┘
```

## Page Designs

### 1. Configuration Page
```
┌─────────────────────────────────────────────────────────────┐
│                    Bot Configuration                        │
├─────────────────────────────────────────────────────────────┤
│ Discord Settings                                            │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Bot Token: ••••••••••••••••••••••••••••••••••••••••••• │ │
│ │ Application ID: 123456789012345678                      │ │
│ │ Guild ID: 987654321098765432 (Optional)                 │ │
│ │ Command Prefix: / (Slash commands)                      │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ AI Model Settings                                           │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Model: Qwen2.5-4B-Instruct ✅ Loaded                   │ │
│ │ Context Window: 32,768 tokens                           │ │
│ │ Temperature: [0.3] ────●──── (0.0 - 1.0)              │ │
│ │ Max Response Length: [2048] tokens                      │ │
│ │ Confidence Threshold: [0.7] ────●──── (0.0 - 1.0)     │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ Hardware Settings                                           │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ GPU Acceleration: ✅ Enabled (AMD RX 7900 GRE)         │ │
│ │ VRAM Limit: [14GB] ────●──── (Max: 20GB)              │ │
│ │ CPU Threads: [12] ────●──── (Max: 16)                 │ │
│ │ Gaming Mode: ✅ Enabled (Limits resource usage)        │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ [💾 Save Changes] [🔄 Reset to Defaults] [🧪 Test Config] │
└─────────────────────────────────────────────────────────────┘
```

### 2. Logs Viewer Page
```
┌─────────────────────────────────────────────────────────────┐
│                        Log Viewer                          │
├─────────────────────────────────────────────────────────────┤
│ Filters: [All Levels ▼] [All Services ▼] [Last 24h ▼]     │
│ Search: [Enter search term...] 🔍                          │
├─────────────────────────────────────────────────────────────┤
│ 2024-01-15 14:32:15 [INFO] Bot Core: Command received      │
│   User: User#1234, Command: /factcheck, Guild: TestServer  │
│                                                             │
│ 2024-01-15 14:32:16 [DEBUG] AI Engine: Processing request  │
│   Statement: "The Earth is flat", Context: 512 tokens      │
│                                                             │
│ 2024-01-15 14:32:17 [INFO] Web Crawler: Fetching sources   │
│   URLs: 3, Timeout: 10s, Cache hits: 1                     │
│                                                             │
│ 2024-01-15 14:32:18 [INFO] AI Engine: Response generated   │
│   Confidence: 0.95, Processing time: 1.8s, VRAM: 2.1GB    │
│                                                             │
│ 2024-01-15 14:32:19 [INFO] Bot Core: Response sent         │
│   Message ID: 123456789, Response time: 2.1s total        │
├─────────────────────────────────────────────────────────────┤
│ [📥 Export Logs] [🗑️ Clear Logs] [⚙️ Log Settings]        │
└─────────────────────────────────────────────────────────────┘
```

### 3. Analytics Page
```
┌─────────────────────────────────────────────────────────────┐
│                       Analytics                             │
├─────────────────────────────────────────────────────────────┤
│ Time Range: [Last 7 days ▼] [Custom Range] [📊 Export]     │
├─────────────────────────────────────────────────────────────┤
│ Usage Trends                                                │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Fact Checks per Day                                     │ │
│ │ 200 ┤                                              ╭─   │ │
│ │ 150 ┤                                         ╭────╯    │ │
│ │ 100 ┤                                    ╭────╯         │ │
│ │  50 ┤                               ╭────╯              │ │
│ │   0 └─┬─────┬─────┬─────┬─────┬─────┬─────┬─────        │ │
│ │      Mon   Tue   Wed   Thu   Fri   Sat   Sun           │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ Performance Metrics                                         │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Response Time Distribution                              │ │
│ │ < 1s  ████████████████████████████████████████ 65%     │ │
│ │ 1-2s  ████████████████████████ 25%                     │ │
│ │ 2-5s  ████████ 8%                                      │ │
│ │ > 5s  ██ 2%                                            │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ Top Fact-Check Categories                                   │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 1. Health & Medicine        ████████████████████ 32%   │ │
│ │ 2. Politics & Government    ████████████████ 24%       │ │
│ │ 3. Science & Technology     ████████████ 18%           │ │
│ │ 4. Climate & Environment    ████████ 12%               │ │
│ │ 5. Economics & Finance      ██████ 9%                  │ │
│ │ 6. Other                    ███ 5%                     │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 4. User Management Page
```
┌─────────────────────────────────────────────────────────────┐
│                    User Management                          │
├─────────────────────────────────────────────────────────────┤
│ Search: [Enter username or ID...] 🔍 [+ Add User]          │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ User#1234 (John Doe)                           [⚙️][🚫] │ │
│ │ Role: Admin • Last Active: 2 hours ago                 │ │
│ │ Fact Checks: 156 • Success Rate: 94% • Avg Time: 1.2s │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ User#5678 (Jane Smith)                         [⚙️][🚫] │ │
│ │ Role: User • Last Active: 1 day ago                    │ │
│ │ Fact Checks: 89 • Success Rate: 91% • Avg Time: 1.8s  │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ Permissions & Roles                                         │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Admin: Full access to all features                     │ │
│ │ Moderator: Can manage users and view logs               │ │
│ │ User: Can use fact-checking commands                    │ │
│ │ Restricted: Limited fact-checking (rate limited)       │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Component Specifications

### Real-time Updates
```typescript
// WebSocket connection for live updates
const useRealTimeData = () => {
  const [data, setData] = useState(null);
  
  useEffect(() => {
    const ws = new WebSocket('ws://localhost:3001/ws');
    
    ws.onmessage = (event) => {
      const update = JSON.parse(event.data);
      setData(prevData => ({
        ...prevData,
        [update.type]: update.data
      }));
    };
    
    return () => ws.close();
  }, []);
  
  return data;
};
```

### Status Indicators
```typescript
const StatusIndicator = ({ status, label }) => {
  const colors = {
    online: 'bg-green-500',
    warning: 'bg-yellow-500',
    error: 'bg-red-500',
    offline: 'bg-gray-500'
  };
  
  return (
    <div className="flex items-center space-x-2">
      <div className={`w-3 h-3 rounded-full ${colors[status]} animate-pulse`} />
      <span className="text-sm font-medium">{label}</span>
    </div>
  );
};
```

### Performance Charts
```typescript
const PerformanceChart = ({ data, title }) => {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg">
      <h3 className="text-lg font-semibold mb-4">{title}</h3>
      <ResponsiveContainer width="100%" height={300}>
        <LineChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="timestamp" />
          <YAxis />
          <Tooltip />
          <Line 
            type="monotone" 
            dataKey="value" 
            stroke="#3B82F6" 
            strokeWidth={2}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};
```

## Theme System

### Color Palette
```css
:root {
  /* Light Theme */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --accent-primary: #3b82f6;
  --accent-secondary: #10b981;
  --border-color: #e2e8f0;
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] {
  /* Dark Theme */
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --text-primary: #f1f5f9;
  --text-secondary: #94a3b8;
  --accent-primary: #60a5fa;
  --accent-secondary: #34d399;
  --border-color: #334155;
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
}
```

### Responsive Design
```css
/* Mobile First Approach */
.dashboard-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .dashboard-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .dashboard-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
```

## Accessibility Features

### Keyboard Navigation
- Full keyboard navigation support
- Focus indicators on all interactive elements
- Skip links for screen readers
- ARIA labels and descriptions

### Screen Reader Support
```typescript
const AccessibleButton = ({ onClick, children, ariaLabel }) => (
  <button
    onClick={onClick}
    aria-label={ariaLabel}
    className="focus:outline-none focus:ring-2 focus:ring-blue-500"
  >
    {children}
  </button>
);
```

### High Contrast Mode
```css
@media (prefers-contrast: high) {
  :root {
    --bg-primary: #000000;
    --text-primary: #ffffff;
    --accent-primary: #ffff00;
    --border-color: #ffffff;
  }
}
```

## Mobile Responsiveness

### Navigation Adaptation
- Collapsible sidebar on mobile
- Bottom navigation bar for quick access
- Swipe gestures for navigation
- Touch-optimized controls

### Layout Adjustments
- Single column layout on mobile
- Larger touch targets (minimum 44px)
- Simplified charts and graphs
- Condensed information display

## Performance Optimization

### Code Splitting
```typescript
// Lazy load dashboard components
const Dashboard = lazy(() => import('./components/Dashboard'));
const Analytics = lazy(() => import('./components/Analytics'));
const Logs = lazy(() => import('./components/Logs'));

// Route-based code splitting
const App = () => (
  <Suspense fallback={<LoadingSpinner />}>
    <Routes>
      <Route path="/" element={<Dashboard />} />
      <Route path="/analytics" element={<Analytics />} />
      <Route path="/logs" element={<Logs />} />
    </Routes>
  </Suspense>
);
```

### Data Optimization
- Virtual scrolling for large log lists
- Pagination for historical data
- Debounced search inputs
- Memoized expensive calculations
- WebSocket connection pooling
