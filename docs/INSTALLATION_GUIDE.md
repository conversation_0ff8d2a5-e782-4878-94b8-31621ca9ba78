# 📦 Installation Guide - Discord AI Fact-Checker Bot

## System Requirements

### Minimum Requirements
- **OS**: Windows 10/11 (64-bit) or Arch Linux
- **CPU**: 4-core processor (Intel i5-8400 / AMD Ryzen 5 2600)
- **RAM**: 8GB system memory
- **GPU**: 4GB VRAM (GTX 1660 / RX 580 or better)
- **Storage**: 10GB free space
- **Network**: Stable internet connection

### Recommended Requirements
- **OS**: Windows 11 or Arch Linux (latest)
- **CPU**: 6+ core processor (Intel i7-10700K / AMD Ryzen 7 3700X+)
- **RAM**: 16GB+ system memory
- **GPU**: 8GB+ VRAM (RTX 3070 / RX 6700 XT or better)
- **Storage**: 20GB free space (NVMe SSD recommended)

## Windows Installation (.exe Installer)

### Step 1: Download Installer
1. Visit the [Releases Page](https://github.com/your-repo/discord-factchecker/releases)
2. Download `DiscordFactChecker-Setup-v1.0.0.exe`
3. Verify the SHA256 checksum (provided on release page)

### Step 2: Run Installation
1. **Right-click** the installer and select **"Run as administrator"**
2. Follow the installation wizard:
   - Accept the license agreement
   - Choose installation directory (default: `C:\Program Files\Discord FactChecker\`)
   - Select components to install:
     - ✅ Core Bot Engine
     - ✅ Web Dashboard
     - ✅ AI Model (Qwen2.5-4B)
     - ✅ GPU Acceleration (CUDA/ROCm)
     - ✅ Windows Service
     - ⬜ Desktop Shortcuts (optional)

### Step 3: Initial Configuration
1. The installer will launch the **Setup Wizard** automatically
2. **Discord Bot Configuration**:
   ```
   Bot Token: [Enter your Discord bot token]
   Application ID: [Enter your Discord application ID]
   Guild ID: [Enter your Discord server ID] (optional)
   ```
3. **Hardware Configuration**:
   ```
   GPU Acceleration: [Auto-detect] ✅ NVIDIA RTX 2070 detected
   VRAM Limit: [6GB] (recommended for gaming)
   CPU Threads: [8] (auto-detected)
   ```
4. **Performance Settings**:
   ```
   Gaming Mode: ✅ Enabled (limits VRAM usage)
   Auto-start with Windows: ✅ Enabled
   Web Dashboard Port: [3000] (default)
   ```

### Step 4: Service Installation
The installer automatically:
- Installs Windows Service (`Discord FactChecker Service`)
- Configures automatic startup
- Sets up firewall rules for local dashboard access
- Creates desktop shortcuts (if selected)

### Step 5: Verification
1. Open **Services** (`services.msc`)
2. Verify **"Discord FactChecker Service"** is running
3. Open web browser to `http://localhost:3000`
4. Check dashboard shows "Bot Status: Online"

## Arch Linux Installation (AUR Package)

### Step 1: Install AUR Helper (if not installed)
```bash
# Install yay (recommended AUR helper)
sudo pacman -S --needed git base-devel
git clone https://aur.archlinux.org/yay.git
cd yay
makepkg -si
```

### Step 2: Install Discord FactChecker
```bash
# Install from AUR
yay -S discord-factchecker-bot

# Or manually with makepkg
git clone https://aur.archlinux.org/discord-factchecker-bot.git
cd discord-factchecker-bot
makepkg -si
```

### Step 3: GPU Driver Setup

#### For AMD GPUs (ROCm)
```bash
# Install ROCm for AMD GPU acceleration
yay -S rocm-hip-sdk rocm-opencl-sdk

# Add user to render group
sudo usermod -a -G render $USER

# Reboot to apply group changes
sudo reboot
```

#### For NVIDIA GPUs (CUDA)
```bash
# Install CUDA toolkit
yay -S cuda cudnn

# Install NVIDIA drivers (if not already installed)
yay -S nvidia nvidia-utils
```

### Step 4: Configuration
```bash
# Create configuration directory
sudo mkdir -p /etc/discord-factchecker

# Copy default configuration
sudo cp /usr/share/discord-factchecker/config.example.yaml /etc/discord-factchecker/config.yaml

# Edit configuration
sudo nano /etc/discord-factchecker/config.yaml
```

**Configuration File** (`/etc/discord-factchecker/config.yaml`):
```yaml
discord:
  token: "YOUR_BOT_TOKEN_HERE"
  application_id: "YOUR_APPLICATION_ID"
  guild_id: "YOUR_GUILD_ID"  # Optional

hardware:
  gpu_acceleration: true
  vram_limit_gb: 6  # Adjust based on your GPU
  cpu_threads: 8    # Auto-detected, adjust if needed
  gaming_mode: true # Limits resource usage

performance:
  max_concurrent_requests: 10
  cache_size_mb: 512
  log_level: "info"

web_dashboard:
  enabled: true
  port: 3000
  bind_address: "127.0.0.1"  # Local access only
  password_protected: false   # Set to true for password protection
```

### Step 5: Service Management
```bash
# Enable and start the service
sudo systemctl enable discord-factchecker
sudo systemctl start discord-factchecker

# Check service status
sudo systemctl status discord-factchecker

# View logs
sudo journalctl -u discord-factchecker -f
```

### Step 6: Verification
```bash
# Check if service is running
systemctl is-active discord-factchecker

# Test web dashboard
curl http://localhost:3000/api/health

# Check GPU utilization (if using AMD)
rocm-smi

# Check GPU utilization (if using NVIDIA)
nvidia-smi
```

## Discord Bot Setup

### Step 1: Create Discord Application
1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Click **"New Application"**
3. Name your application (e.g., "Fact Checker Bot")
4. Go to **"Bot"** section
5. Click **"Add Bot"**
6. **Copy the Bot Token** (keep this secure!)

### Step 2: Configure Bot Permissions
Required permissions:
- ✅ Send Messages
- ✅ Use Slash Commands
- ✅ Read Message History
- ✅ Add Reactions
- ✅ Embed Links
- ✅ Attach Files
- ✅ Use External Emojis

Permission Integer: `274877975552`

### Step 3: Invite Bot to Server
1. Go to **"OAuth2"** → **"URL Generator"**
2. Select **"bot"** and **"applications.commands"** scopes
3. Select the permissions listed above
4. Copy the generated URL and open in browser
5. Select your Discord server and authorize

### Step 4: Configure Slash Commands
The bot automatically registers these commands:
- `/factcheck <statement>` - Manual fact-checking
- `/configure` - Bot configuration (admin only)
- `/stats` - Usage statistics
- `/help` - Command help

## Post-Installation Configuration

### Web Dashboard Access
1. Open browser to `http://localhost:3000`
2. Navigate through the setup wizard:
   - **Bot Status**: Verify connection to Discord
   - **AI Model**: Confirm model is loaded
   - **GPU Status**: Check GPU acceleration
   - **Performance**: Review system metrics

### Gaming Mode Configuration
To ensure the bot doesn't interfere with gaming:

1. **VRAM Limit**: Set to 70% of total VRAM
2. **CPU Priority**: Set to "Below Normal"
3. **Auto-pause**: Enable during full-screen games
4. **Scheduled Processing**: Process fact-checks during low activity

### Channel Configuration
Configure which channels the bot monitors:
1. Go to Dashboard → **Channel Settings**
2. **Auto Fact-Check Channels**: Select channels for automatic monitoring
3. **Command Channels**: Channels where slash commands work
4. **Admin Channels**: Channels for bot management commands

## Troubleshooting

### Common Issues

#### Bot Not Responding
```bash
# Check service status
systemctl status discord-factchecker  # Linux
# Or check Windows Services for "Discord FactChecker Service"

# Check logs
journalctl -u discord-factchecker -n 50  # Linux
# Or check Windows Event Viewer
```

#### GPU Not Detected
```bash
# AMD GPU troubleshooting
rocm-smi  # Should show your GPU
export HSA_OVERRIDE_GFX_VERSION=10.3.0  # For unsupported GPUs

# NVIDIA GPU troubleshooting
nvidia-smi  # Should show your GPU
export CUDA_VISIBLE_DEVICES=0  # Force GPU 0
```

#### High Memory Usage
1. Reduce VRAM limit in configuration
2. Lower `max_concurrent_requests`
3. Reduce `cache_size_mb`
4. Enable `gaming_mode`

#### Web Dashboard Not Accessible
```bash
# Check if port is in use
netstat -tulpn | grep :3000

# Check firewall (Linux)
sudo ufw status
sudo ufw allow 3000/tcp

# Check Windows Firewall
# Windows Defender Firewall → Allow an app
```

### Getting Help
- **Documentation**: Check `/usr/share/doc/discord-factchecker/` (Linux)
- **Logs**: Always check logs first for error messages
- **Community**: Join our Discord server for support
- **Issues**: Report bugs on GitHub Issues page

## Updating

### Windows Update
The bot includes an auto-updater that:
- Checks for updates daily
- Downloads updates in background
- Prompts for installation during low activity
- Automatically backs up configuration

### Arch Linux Update
```bash
# Update via AUR helper
yay -Syu discord-factchecker-bot

# Or manually
cd discord-factchecker-bot
git pull
makepkg -si
```

## Uninstallation

### Windows
1. **Control Panel** → **Programs and Features**
2. Select **"Discord FactChecker"**
3. Click **"Uninstall"**
4. Follow uninstall wizard
5. Optionally remove configuration files from `%APPDATA%\Discord FactChecker\`

### Arch Linux
```bash
# Remove package
yay -R discord-factchecker-bot

# Remove configuration (optional)
sudo rm -rf /etc/discord-factchecker

# Remove user data (optional)
rm -rf ~/.local/share/discord-factchecker
```
