# 📦 Packaging Guide - Discord AI Fact-Checker Bot

## Overview
This document outlines the packaging requirements and processes for distributing the Discord AI Fact-Checker Bot across Windows (.exe installer) and Arch Linux (AUR package) platforms.

## Windows Packaging (.exe Installer)

### Build Environment Setup
```powershell
# Required tools
choco install nodejs python git visualstudio2022buildtools
choco install nsis nsis-unicode  # For installer creation
choco install signtool           # For code signing

# CUDA Toolkit (for NVIDIA support)
choco install cuda

# ROCm SDK (for AMD support) - Manual installation required
# Download from: https://rocm.docs.amd.com/en/latest/deploy/windows/quick_start.html
```

### Project Structure
```
discord-factchecker-windows/
├── src/                          # Source code
│   ├── bot/                      # Node.js bot core
│   ├── ai/                       # Python AI engine
│   ├── crawler/                  # Web crawling service
│   └── dashboard/                # React web interface
├── build/                        # Build scripts and configuration
│   ├── windows/
│   │   ├── installer.nsi         # NSIS installer script
│   │   ├── build.ps1            # PowerShell build script
│   │   ├── sign.ps1             # Code signing script
│   │   └── package.json         # Build dependencies
├── assets/                       # Installer assets
│   ├── icons/                   # Application icons
│   ├── images/                  # Installer graphics
│   └── licenses/                # License files
├── models/                       # AI model files
│   └── qwen2.5-4b-instruct.gguf # Quantized model
└── dist/                        # Build output
    └── DiscordFactChecker-Setup-v1.0.0.exe
```

### NSIS Installer Script (`build/windows/installer.nsi`)
```nsis
!define PRODUCT_NAME "Discord FactChecker"
!define PRODUCT_VERSION "1.0.0"
!define PRODUCT_PUBLISHER "Your Company"
!define PRODUCT_WEB_SITE "https://github.com/your-repo"
!define PRODUCT_DIR_REGKEY "Software\Microsoft\Windows\CurrentVersion\App Paths\discord-factchecker.exe"
!define PRODUCT_UNINST_KEY "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}"

!include "MUI2.nsh"
!include "x64.nsh"
!include "WinVer.nsh"

; Installer settings
Name "${PRODUCT_NAME} ${PRODUCT_VERSION}"
OutFile "..\..\dist\DiscordFactChecker-Setup-v${PRODUCT_VERSION}.exe"
InstallDir "$PROGRAMFILES64\Discord FactChecker"
ShowInstDetails show
ShowUnInstDetails show

; Modern UI configuration
!define MUI_ABORTWARNING
!define MUI_ICON "..\..\assets\icons\app.ico"
!define MUI_UNICON "..\..\assets\icons\uninstall.ico"
!define MUI_WELCOMEFINISHPAGE_BITMAP "..\..\assets\images\wizard.bmp"

; Pages
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_LICENSE "..\..\assets\licenses\LICENSE.txt"
!insertmacro MUI_PAGE_COMPONENTS
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES

!insertmacro MUI_LANGUAGE "English"

; Installation sections
Section "Core Application" SecCore
  SectionIn RO  ; Required section
  
  SetOutPath "$INSTDIR"
  File /r "..\..\src\*"
  File "..\..\models\qwen2.5-4b-instruct.gguf"
  
  ; Create Windows Service
  ExecWait '"$INSTDIR\bin\install-service.exe" install'
  
  ; Create shortcuts
  CreateDirectory "$SMPROGRAMS\Discord FactChecker"
  CreateShortCut "$SMPROGRAMS\Discord FactChecker\Discord FactChecker.lnk" "$INSTDIR\discord-factchecker.exe"
  CreateShortCut "$SMPROGRAMS\Discord FactChecker\Web Dashboard.lnk" "http://localhost:3000"
  
  ; Registry entries
  WriteRegStr HKLM "${PRODUCT_DIR_REGKEY}" "" "$INSTDIR\discord-factchecker.exe"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "DisplayName" "$(^Name)"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "UninstallString" "$INSTDIR\uninst.exe"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "DisplayVersion" "${PRODUCT_VERSION}"
  WriteRegStr HKLM "${PRODUCT_UNINST_KEY}" "Publisher" "${PRODUCT_PUBLISHER}"
  
  WriteUninstaller "$INSTDIR\uninst.exe"
SectionEnd

Section "GPU Acceleration" SecGPU
  ; Install CUDA/ROCm runtime libraries
  SetOutPath "$INSTDIR\gpu"
  File /r "..\..\gpu-libs\*"
SectionEnd

Section "Desktop Shortcuts" SecShortcuts
  CreateShortCut "$DESKTOP\Discord FactChecker.lnk" "$INSTDIR\discord-factchecker.exe"
SectionEnd

Section "Auto-start with Windows" SecAutoStart
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Run" "DiscordFactChecker" "$INSTDIR\discord-factchecker.exe --service"
SectionEnd
```

### Build Script (`build/windows/build.ps1`)
```powershell
param(
    [string]$Version = "1.0.0",
    [switch]$Sign = $false
)

Write-Host "Building Discord FactChecker v$Version for Windows..."

# Clean previous builds
Remove-Item -Path "dist" -Recurse -Force -ErrorAction SilentlyContinue
New-Item -ItemType Directory -Path "dist" -Force

# Build Node.js application
Write-Host "Building Node.js bot core..."
Set-Location "src/bot"
npm ci --production
npm run build
Set-Location "../.."

# Build Python AI engine
Write-Host "Building Python AI engine..."
Set-Location "src/ai"
python -m pip install -r requirements.txt
python -m PyInstaller --onefile --distpath "../../dist/ai" main.py
Set-Location "../.."

# Build React dashboard
Write-Host "Building React dashboard..."
Set-Location "src/dashboard"
npm ci
npm run build
Set-Location "../.."

# Download AI model
Write-Host "Downloading AI model..."
if (!(Test-Path "models/qwen2.5-4b-instruct.gguf")) {
    New-Item -ItemType Directory -Path "models" -Force
    Invoke-WebRequest -Uri "https://huggingface.co/Qwen/Qwen2.5-4B-Instruct-GGUF/resolve/main/qwen2.5-4b-instruct-q4_0.gguf" -OutFile "models/qwen2.5-4b-instruct.gguf"
}

# Create installer
Write-Host "Creating installer..."
& "C:\Program Files (x86)\NSIS\Unicode\makensis.exe" "build/windows/installer.nsi"

# Sign installer if requested
if ($Sign) {
    Write-Host "Signing installer..."
    & "build/windows/sign.ps1" -FilePath "dist/DiscordFactChecker-Setup-v$Version.exe"
}

Write-Host "Build complete! Installer: dist/DiscordFactChecker-Setup-v$Version.exe"
```

### Code Signing Script (`build/windows/sign.ps1`)
```powershell
param(
    [Parameter(Mandatory=$true)]
    [string]$FilePath
)

# Code signing certificate (from environment or certificate store)
$CertThumbprint = $env:CODE_SIGN_CERT_THUMBPRINT
$TimestampServer = "http://timestamp.digicert.com"

if (-not $CertThumbprint) {
    Write-Error "Code signing certificate thumbprint not found in environment variable CODE_SIGN_CERT_THUMBPRINT"
    exit 1
}

Write-Host "Signing $FilePath..."
& signtool sign /sha1 $CertThumbprint /t $TimestampServer /v $FilePath

if ($LASTEXITCODE -eq 0) {
    Write-Host "Successfully signed $FilePath"
} else {
    Write-Error "Failed to sign $FilePath"
    exit 1
}
```

## Arch Linux Packaging (AUR)

### PKGBUILD Structure
```bash
# PKGBUILD for discord-factchecker-bot
pkgname=discord-factchecker-bot
pkgver=1.0.0
pkgrel=1
pkgdesc="Local GPU-accelerated Discord AI fact-checking bot"
arch=('x86_64')
url="https://github.com/your-repo/discord-factchecker"
license=('MIT')
depends=(
    'nodejs>=18'
    'python>=3.11'
    'sqlite'
    'chromium'  # For web crawling
)
makedepends=(
    'npm'
    'python-pip'
    'git'
)
optdepends=(
    'cuda: NVIDIA GPU acceleration'
    'rocm-hip-sdk: AMD GPU acceleration'
    'rocm-opencl-sdk: AMD OpenCL support'
)
backup=(
    'etc/discord-factchecker/config.yaml'
)
source=(
    "discord-factchecker-$pkgver.tar.gz::$url/archive/v$pkgver.tar.gz"
    "discord-factchecker.service"
    "discord-factchecker.sysusers"
    "discord-factchecker.tmpfiles"
)
sha256sums=(
    'SKIP'  # Will be updated with actual checksums
    'SKIP'
    'SKIP'
    'SKIP'
)

prepare() {
    cd "$srcdir/discord-factchecker-$pkgver"
    
    # Download AI model
    mkdir -p models
    if [ ! -f "models/qwen2.5-4b-instruct.gguf" ]; then
        curl -L "https://huggingface.co/Qwen/Qwen2.5-4B-Instruct-GGUF/resolve/main/qwen2.5-4b-instruct-q4_0.gguf" \
             -o "models/qwen2.5-4b-instruct.gguf"
    fi
}

build() {
    cd "$srcdir/discord-factchecker-$pkgver"
    
    # Build Node.js bot core
    cd src/bot
    npm ci --production
    npm run build
    cd ../..
    
    # Build Python AI engine
    cd src/ai
    pip install --user -r requirements.txt
    cd ../..
    
    # Build React dashboard
    cd src/dashboard
    npm ci
    npm run build
    cd ../..
}

package() {
    cd "$srcdir/discord-factchecker-$pkgver"
    
    # Install application files
    install -dm755 "$pkgdir/usr/lib/discord-factchecker"
    cp -r src/* "$pkgdir/usr/lib/discord-factchecker/"
    cp -r models "$pkgdir/usr/lib/discord-factchecker/"
    
    # Install configuration
    install -Dm644 config.example.yaml "$pkgdir/etc/discord-factchecker/config.yaml"
    
    # Install systemd service
    install -Dm644 "$srcdir/discord-factchecker.service" \
                   "$pkgdir/usr/lib/systemd/system/discord-factchecker.service"
    
    # Install sysusers and tmpfiles
    install -Dm644 "$srcdir/discord-factchecker.sysusers" \
                   "$pkgdir/usr/lib/sysusers.d/discord-factchecker.conf"
    install -Dm644 "$srcdir/discord-factchecker.tmpfiles" \
                   "$pkgdir/usr/lib/tmpfiles.d/discord-factchecker.conf"
    
    # Install executable
    install -dm755 "$pkgdir/usr/bin"
    cat > "$pkgdir/usr/bin/discord-factchecker" << 'EOF'
#!/bin/bash
cd /usr/lib/discord-factchecker
exec node bot/index.js "$@"
EOF
    chmod +x "$pkgdir/usr/bin/discord-factchecker"
    
    # Install documentation
    install -Dm644 README.md "$pkgdir/usr/share/doc/discord-factchecker/README.md"
    install -Dm644 LICENSE "$pkgdir/usr/share/licenses/discord-factchecker/LICENSE"
}
```

### Systemd Service (`discord-factchecker.service`)
```ini
[Unit]
Description=Discord AI Fact-Checker Bot
After=network-online.target
Wants=network-online.target

[Service]
Type=simple
User=discord-factchecker
Group=discord-factchecker
WorkingDirectory=/usr/lib/discord-factchecker
ExecStart=/usr/bin/discord-factchecker
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/lib/discord-factchecker /var/log/discord-factchecker

# Resource limits
MemoryMax=8G
CPUQuota=80%

[Install]
WantedBy=multi-user.target
```

### System Users (`discord-factchecker.sysusers`)
```
u discord-factchecker - "Discord FactChecker Bot" /var/lib/discord-factchecker /bin/nologin
```

### Temporary Files (`discord-factchecker.tmpfiles`)
```
d /var/lib/discord-factchecker 0755 discord-factchecker discord-factchecker
d /var/log/discord-factchecker 0755 discord-factchecker discord-factchecker
d /run/discord-factchecker 0755 discord-factchecker discord-factchecker
```

## Cross-Platform Build Automation

### GitHub Actions Workflow (`.github/workflows/build.yml`)
```yaml
name: Build and Release

on:
  push:
    tags: ['v*']
  workflow_dispatch:

jobs:
  build-windows:
    runs-on: windows-latest
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          
      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'
          
      - name: Install NSIS
        run: choco install nsis
        
      - name: Build Windows Installer
        run: |
          powershell -ExecutionPolicy Bypass -File build/windows/build.ps1 -Version ${{ github.ref_name }}
          
      - name: Upload Windows Installer
        uses: actions/upload-artifact@v4
        with:
          name: windows-installer
          path: dist/*.exe

  build-arch:
    runs-on: ubuntu-latest
    container: archlinux:latest
    steps:
      - name: Install dependencies
        run: |
          pacman -Syu --noconfirm base-devel git nodejs npm python python-pip
          
      - uses: actions/checkout@v4
      
      - name: Build AUR package
        run: |
          useradd -m builder
          chown -R builder:builder .
          sudo -u builder makepkg -s --noconfirm
          
      - name: Upload AUR package
        uses: actions/upload-artifact@v4
        with:
          name: arch-package
          path: '*.pkg.tar.zst'

  release:
    needs: [build-windows, build-arch]
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/')
    steps:
      - name: Download artifacts
        uses: actions/download-artifact@v4
        
      - name: Create Release
        uses: softprops/action-gh-release@v1
        with:
          files: |
            windows-installer/*.exe
            arch-package/*.pkg.tar.zst
          generate_release_notes: true
```

## Distribution Strategy

### Windows Distribution
1. **Primary**: GitHub Releases with signed .exe installer
2. **Alternative**: Microsoft Store (future consideration)
3. **Enterprise**: MSI package for corporate deployment

### Arch Linux Distribution
1. **Primary**: AUR (Arch User Repository)
2. **Alternative**: Custom repository for pre-built packages
3. **Container**: Docker image for other Linux distributions

### Update Mechanism
- **Windows**: Built-in auto-updater with delta updates
- **Arch Linux**: Standard pacman update process
- **Fallback**: Manual download from GitHub Releases

## Quality Assurance

### Testing Pipeline
1. **Unit Tests**: Automated testing of core components
2. **Integration Tests**: End-to-end Discord bot functionality
3. **Performance Tests**: GPU acceleration and memory usage
4. **Security Scans**: Vulnerability assessment of dependencies
5. **Compatibility Tests**: Multiple GPU vendors and OS versions

### Release Checklist
- [ ] All tests passing
- [ ] Security scan clean
- [ ] Documentation updated
- [ ] Version numbers incremented
- [ ] Changelog updated
- [ ] Code signed (Windows)
- [ ] AUR package tested
- [ ] Release notes prepared

## Security Considerations

### Code Signing
- **Windows**: Authenticode certificate for installer and executables
- **Checksums**: SHA256 hashes for all distributed files
- **Verification**: Public key verification for updates

### Supply Chain Security
- **Dependencies**: Regular security audits of npm/pip packages
- **Build Environment**: Isolated build containers
- **Reproducible Builds**: Deterministic build process
