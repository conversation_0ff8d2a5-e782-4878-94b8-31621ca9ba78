# 🛠️ Development Guide - Discord AI Fact-Checker Bot

## Development Environment Setup

### Prerequisites
- **Node.js**: v18+ with npm
- **Python**: 3.11+ with pip
- **Git**: Latest version
- **GPU Drivers**: CUDA 12.0+ (NVIDIA) or ROCm 5.7+ (AMD)
- **IDE**: VS Code with recommended extensions

### Required VS Code Extensions
```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "ms-python.python",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.test-adapter-converter",
    "vitest.explorer"
  ]
}
```

### Environment Setup
```bash
# Clone repository
git clone https://github.com/your-repo/discord-factchecker.git
cd discord-factchecker

# Install Node.js dependencies
cd src/bot && npm install && cd ../..
cd src/dashboard && npm install && cd ../..

# Install Python dependencies
cd src/ai && pip install -r requirements.txt && cd ../..
cd src/crawler && pip install -r requirements.txt && cd ../..

# Setup environment variables
cp .env.example .env
# Edit .env with your configuration
```

### Development Configuration (`.env`)
```env
# Discord Configuration
DISCORD_TOKEN=your_bot_token_here
DISCORD_APPLICATION_ID=your_application_id
DISCORD_GUILD_ID=your_test_guild_id

# AI Configuration
AI_MODEL_PATH=./models/qwen2.5-4b-instruct.gguf
AI_CONTEXT_WINDOW=32768
AI_TEMPERATURE=0.3
AI_MAX_TOKENS=2048

# Hardware Configuration
GPU_ACCELERATION=true
VRAM_LIMIT_GB=8
CPU_THREADS=8
GAMING_MODE=true

# Service Ports
BOT_PORT=3001
AI_PORT=8000
CRAWLER_PORT=8001
DASHBOARD_PORT=3000

# Database
DATABASE_URL=sqlite:./data/factchecker.db
CACHE_DATABASE_URL=sqlite:./data/cache.db

# Logging
LOG_LEVEL=debug
LOG_FILE=./logs/factchecker.log
```

## Project Structure

### Repository Layout
```
discord-factchecker/
├── .github/                     # GitHub workflows and templates
│   ├── workflows/              # CI/CD pipelines
│   └── instructions/           # Development guidelines
├── src/                        # Source code
│   ├── bot/                    # Discord bot (Node.js/TypeScript)
│   ├── ai/                     # AI inference engine (Python)
│   ├── crawler/                # Web crawling service (Python)
│   ├── dashboard/              # Web dashboard (React/TypeScript)
│   └── shared/                 # Shared utilities and types
├── tests/                      # Test suites
│   ├── unit/                   # Unit tests
│   ├── integration/            # Integration tests
│   └── e2e/                    # End-to-end tests
├── docs/                       # Documentation
├── models/                     # AI model files
├── data/                       # Database files
├── logs/                       # Log files
├── build/                      # Build scripts and configuration
└── dist/                       # Build output
```

### Bot Core Architecture (`src/bot/`)
```
bot/
├── index.ts                    # Main entry point
├── config/                     # Configuration management
│   ├── config.ts              # Configuration loader
│   └── validation.ts          # Config validation
├── commands/                   # Slash command handlers
│   ├── factcheck.ts           # Manual fact-checking
│   ├── configure.ts           # Bot configuration
│   ├── stats.ts               # Usage statistics
│   └── help.ts                # Help command
├── events/                     # Discord event handlers
│   ├── ready.ts               # Bot startup
│   ├── messageCreate.ts       # Message processing
│   ├── interactionCreate.ts   # Slash command handling
│   └── error.ts               # Error handling
├── services/                   # Core business logic
│   ├── aiService.ts           # AI engine communication
│   ├── crawlService.ts        # Web crawling integration
│   ├── cacheService.ts        # Caching layer
│   ├── queueService.ts        # Request queue management
│   └── configService.ts       # Configuration management
├── utils/                      # Utility functions
│   ├── logger.ts              # Structured logging
│   ├── rateLimiter.ts         # Rate limiting
│   ├── contextManager.ts      # Conversation context
│   ├── errorHandler.ts        # Error handling
│   └── validators.ts          # Input validation
└── types/                      # TypeScript type definitions
    ├── discord.ts             # Discord-specific types
    ├── ai.ts                  # AI service types
    └── config.ts              # Configuration types
```

## Development Workflow

### Git Workflow
```bash
# Create feature branch
git checkout -b feature/new-feature

# Make changes and commit
git add .
git commit -m "feat: add new feature"

# Push and create PR
git push origin feature/new-feature
# Create PR via GitHub UI
```

### Commit Message Convention
```
type(scope): description

Types:
- feat: New feature
- fix: Bug fix
- docs: Documentation changes
- style: Code style changes
- refactor: Code refactoring
- test: Test changes
- chore: Build/tooling changes

Examples:
feat(bot): add automatic fact-checking
fix(ai): resolve GPU memory leak
docs(readme): update installation instructions
```

### Testing Strategy

#### Unit Testing
```bash
# Run all unit tests
npm run test:unit

# Run specific test file
npm run test:unit -- factcheck.test.ts

# Run with coverage
npm run test:coverage
```

#### Integration Testing
```bash
# Run integration tests
npm run test:integration

# Test Discord integration
npm run test:discord

# Test AI engine integration
npm run test:ai
```

#### End-to-End Testing
```bash
# Run E2E tests
npm run test:e2e

# Run specific E2E scenario
npm run test:e2e -- fact-checking-flow
```

### Code Quality Tools

#### ESLint Configuration (`.eslintrc.js`)
```javascript
module.exports = {
  extends: [
    '@typescript-eslint/recommended',
    'prettier'
  ],
  parser: '@typescript-eslint/parser',
  plugins: ['@typescript-eslint'],
  rules: {
    '@typescript-eslint/no-unused-vars': 'error',
    '@typescript-eslint/explicit-function-return-type': 'warn',
    'prefer-const': 'error',
    'no-var': 'error'
  }
};
```

#### Prettier Configuration (`.prettierrc`)
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
```

#### Pre-commit Hooks (`package.json`)
```json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged",
      "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"
    }
  },
  "lint-staged": {
    "*.{ts,tsx}": [
      "eslint --fix",
      "prettier --write",
      "git add"
    ],
    "*.{py}": [
      "black",
      "flake8",
      "git add"
    ]
  }
}
```

## Development Scripts

### Package.json Scripts
```json
{
  "scripts": {
    "dev": "concurrently \"npm run dev:bot\" \"npm run dev:ai\" \"npm run dev:dashboard\"",
    "dev:bot": "cd src/bot && npm run dev",
    "dev:ai": "cd src/ai && python main.py --dev",
    "dev:dashboard": "cd src/dashboard && npm run dev",
    "build": "npm run build:bot && npm run build:ai && npm run build:dashboard",
    "build:bot": "cd src/bot && npm run build",
    "build:ai": "cd src/ai && python -m PyInstaller main.py",
    "build:dashboard": "cd src/dashboard && npm run build",
    "test": "npm run test:unit && npm run test:integration",
    "test:unit": "vitest run",
    "test:integration": "vitest run --config vitest.integration.config.ts",
    "test:e2e": "playwright test",
    "test:coverage": "vitest run --coverage",
    "lint": "eslint src/**/*.ts",
    "lint:fix": "eslint src/**/*.ts --fix",
    "format": "prettier --write src/**/*.{ts,tsx}",
    "type-check": "tsc --noEmit"
  }
}
```

### Development Makefile
```makefile
.PHONY: dev build test clean install

# Development
dev:
	npm run dev

dev-bot:
	cd src/bot && npm run dev

dev-ai:
	cd src/ai && python main.py --dev

dev-dashboard:
	cd src/dashboard && npm run dev

# Building
build:
	npm run build

build-windows:
	powershell -ExecutionPolicy Bypass -File build/windows/build.ps1

build-arch:
	makepkg -s

# Testing
test:
	npm run test

test-unit:
	npm run test:unit

test-integration:
	npm run test:integration

test-e2e:
	npm run test:e2e

# Utilities
install:
	npm install
	cd src/bot && npm install
	cd src/dashboard && npm install
	cd src/ai && pip install -r requirements.txt
	cd src/crawler && pip install -r requirements.txt

clean:
	rm -rf node_modules
	rm -rf src/*/node_modules
	rm -rf dist
	rm -rf build/output

lint:
	npm run lint

format:
	npm run format

type-check:
	npm run type-check
```

## Debugging

### VS Code Launch Configuration (`.vscode/launch.json`)
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Bot",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/src/bot/index.ts",
      "outFiles": ["${workspaceFolder}/src/bot/dist/**/*.js"],
      "env": {
        "NODE_ENV": "development"
      },
      "console": "integratedTerminal",
      "skipFiles": ["<node_internals>/**"]
    },
    {
      "name": "Debug AI Engine",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/src/ai/main.py",
      "args": ["--dev"],
      "console": "integratedTerminal",
      "cwd": "${workspaceFolder}/src/ai"
    },
    {
      "name": "Debug Tests",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/node_modules/vitest/vitest.mjs",
      "args": ["run", "--reporter=verbose"],
      "console": "integratedTerminal",
      "skipFiles": ["<node_internals>/**"]
    }
  ]
}
```

### Logging Configuration
```typescript
// src/bot/utils/logger.ts
import winston from 'winston';

export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ 
      filename: 'logs/error.log', 
      level: 'error' 
    }),
    new winston.transports.File({ 
      filename: 'logs/combined.log' 
    }),
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    })
  ]
});
```

## Performance Optimization

### GPU Memory Profiling
```python
# src/ai/utils/memory_profiler.py
import torch
import psutil
from typing import Dict, Any

class MemoryProfiler:
    def __init__(self):
        self.gpu_available = torch.cuda.is_available()
        
    def get_memory_stats(self) -> Dict[str, Any]:
        stats = {
            'cpu_percent': psutil.cpu_percent(),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_usage': psutil.disk_usage('/').percent
        }
        
        if self.gpu_available:
            stats.update({
                'gpu_memory_allocated': torch.cuda.memory_allocated(),
                'gpu_memory_reserved': torch.cuda.memory_reserved(),
                'gpu_memory_free': torch.cuda.get_device_properties(0).total_memory - torch.cuda.memory_allocated()
            })
            
        return stats
```

### Performance Monitoring
```typescript
// src/bot/utils/performance.ts
export class PerformanceMonitor {
  private metrics: Map<string, number[]> = new Map();
  
  startTimer(operation: string): () => void {
    const start = performance.now();
    
    return () => {
      const duration = performance.now() - start;
      this.recordMetric(operation, duration);
    };
  }
  
  recordMetric(name: string, value: number): void {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    
    const values = this.metrics.get(name)!;
    values.push(value);
    
    // Keep only last 100 measurements
    if (values.length > 100) {
      values.shift();
    }
  }
  
  getAverageMetric(name: string): number {
    const values = this.metrics.get(name) || [];
    return values.reduce((a, b) => a + b, 0) / values.length;
  }
}
```

## Contributing Guidelines

### Pull Request Process
1. Fork the repository
2. Create a feature branch
3. Make changes with tests
4. Ensure all tests pass
5. Update documentation
6. Submit pull request

### Code Review Checklist
- [ ] Code follows style guidelines
- [ ] Tests are included and passing
- [ ] Documentation is updated
- [ ] No security vulnerabilities
- [ ] Performance impact considered
- [ ] Backward compatibility maintained
