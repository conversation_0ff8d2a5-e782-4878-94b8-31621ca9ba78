const Joi = require('joi');

// Test discord schema
const discordSchema = Joi.object({
  token: Joi.string().required().min(50).description('Discord bot token'),
  clientId: Joi.string().required().pattern(/^\d+$/).description('Discord client ID'),
  guildId: Joi.string().optional().pattern(/^\d+$/).description('Discord guild ID for development'),
  commandPrefix: Joi.string().default('!').max(5).description('Command prefix'),
  autoFactCheck: Joi.boolean().default(true).description('Enable automatic fact checking'),
  confidenceThreshold: Joi.number().min(0).max(1).default(0.7).description('Minimum confidence for responses'),
  responseDelay: Joi.number().min(0).max(60).default(2).description('Delay before responding (seconds)'),
  maxResponseLength: Joi.number().min(500).max(4000).default(2000).description('Maximum response length'),
  sourceCount: Joi.number().min(1).max(10).default(3).description('Number of sources to verify')
}).unknown(true).options({ stripUnknown: true });

// Test main schema
const configSchema = Joi.object({
  discord: discordSchema.optional(),
}).unknown(true).options({ stripUnknown: true }).default();

// Test data
const configWithUnknown = {
  discord: {
    token: 'valid-discord-token-with-minimum-50-characters-required',
    clientId: '123456789012345678',
    unknownField: 'should-be-removed'
  },
  unknownSection: {
    someValue: 'should-be-removed'
  }
};

console.log('Input:', JSON.stringify(configWithUnknown, null, 2));

const result = configSchema.validate(configWithUnknown);

console.log('Error:', result.error);
console.log('Result:', JSON.stringify(result.value, null, 2));
console.log('Discord has unknownField:', result.value?.discord?.hasOwnProperty('unknownField'));
console.log('Result has unknownSection:', result.value?.hasOwnProperty('unknownSection'));
