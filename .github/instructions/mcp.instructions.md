# 🔌 Model Context Protocol (MCP) Instructions

Copilot evaluates these rules silently on every request.

## Available MCP Servers & Tool Calls

### discord-mcp
- send-message
  • Description: Send text or embed messages to a Discord channel.
  • Usage: mcp run "discord-mcp" --tool "send-message" --args '{"channel":"<id>","content":"<text or embed JSON>"}'
  • When: After generating or formatting a response to users.
  • Pair With: code-mcp (to format message content), cache-mcp (to avoid duplicate posts).

- listen-event
  • Description: Subscribe to Discord events (e.g., messageCreate, guildMemberAdd).
  • Usage: mcp run "discord-mcp" --tool "listen-event" --args '{"event":"<event>","handler":"<function>"}'
  • When: During bot initialization or dynamic handler registration.
  • Pair With: sequential-think (to plan handler logic), code-mcp (to scaffold handler).

- edit-message
  • Description: Modify an existing message by ID.
  • Usage: mcp run "discord-mcp" --tool "edit-message" --args '{"channel":"<id>","messageId":"<id>","content":"<new content>"}'
  • When: Providing updates or corrections to previous responses.
  • Pair With: context7-mcp (to fetch corrected info).


### context7-mcp
- search-docs
  • Description: Query aggregated technical documentation and retrieve relevant snippets.
  • Usage: mcp run "context7-mcp" --tool "search-docs" --args '{"query":"<topic>"}'
  • When: Looking up API references or verifying implementation details.
  • Pair With: code-mcp (to implement based on docs).

- get-library-docs
  • Description: Fetch full documentation for a specific library or function.
  • Usage: mcp run "context7-mcp" --tool "get-library-docs" --args '{"library":"<name>","topic":"<subtopic>"}'
  • When: Deep dives into complex APIs or modules.
  • Pair With: sequential-think (to plan multi-step usage).

- fact-check
  • Description: Validate a statement against known sources, returning verdict and citations.
  • Usage: mcp run "context7-mcp" --tool "fact-check" --args '{"statement":"<text>"}'
  • When: Checking user claims or external data correctness.
  • Pair With: cache-mcp (to record results for reuse), code-mcp (to format response).


### cache-mcp
- get-cache
  • Description: Retrieve stored data by key.
  • Usage: mcp run "cache-mcp" --tool "get" --args '{"key":"<cacheKey>"}'
  • When: Before making expensive or repeated calls (Discord or Context7).
  • Pair With: context7-mcp, discord-mcp.

- set-cache
  • Description: Store data with TTL.
  • Usage: mcp run "cache-mcp" --tool "set" --args '{"key":"<cacheKey>","value":<data>,"ttl":<seconds>}'

  • When: After obtaining new data that may be reused.
  • Pair With: fact-check, search-docs.

- clear-cache
  • Description: Invalidate a specific key or all entries.
  • Usage: mcp run "cache-mcp" --tool "clear" --args '{"key":"<cacheKey>"}'
  • When: After deploys, schema changes, or stale data issues.


### toolbox-mcp
- discover-server
  • Description: Query the registry of MCP servers and tools for best fit.
  • Usage: mcp run "toolbox-mcp" --tool "discover-server" --args '{"task":"<decho $EXA_API_KEY  escription>"}'
  • When: Uncertain which MCP server or tool to use.
  • Pair With: any other MCP run call.


### sequential-think
- plan
  • Description: Generate step-by-step breakdown of complex tasks.
  • Usage: mcp run "sequential-think" --tool "plan" --args '{"goal":"<objective>"}'
  • When: Starting a multi-step feature or debugging workflow.
  • Pair With: code-mcp (to implement each step).

- decompose
  • Description: Split a large task into independent subtasks.
  • Usage: mcp run "sequential-think" --tool "decompose" --args '{"task":"<description>"}'
  • When: Refining tasks for shrimp-task manager.


### shrimp-task
- create-task
  • Description: Add a new task with details and dependencies.
  • Usage: mcp run "shrimp-task" --tool "create-task" --args '{"name":"<title>","description":"<details>"}'
  • When: Defining work items for feature development or bug fixes.
  • Pair With: sequential-think.decompose.

- list-tasks
  • Description: Retrieve current task list and statuses.
  • Usage: mcp run "shrimp-task" --tool "list-tasks" --args '{}'
  • When: Checking progress or delegating work.

- complete-task
  • Description: Mark a task as done.
  • Usage: mcp run "shrimp-task" --tool "complete-task" --args '{"taskId":"<id>"}'
  • When: After verification and testing of a task.


### code-review-mcp
- review-code
  • Description: Analyze code changes and comment on potential issues.
  • Usage: mcp run "code-review-mcp" --tool "review-code" --args '{"files":["<paths>"]}'
  • When: After generating or modifying code blocks.
  • Pair With: code-mcp (for iterative improvements).

- suggest-improvement
  • Description: Provide refactoring or best-practice suggestions.
  • Usage: mcp run "code-review-mcp" --tool "suggest-improvement" --args '{"file":"<path>"}'
  • When: Seeking optimization or style alignment.


### vibe-check-mcp
- analyze-tone
  • Description: Evaluate generated text or code comments for tone and clarity.
  • Usage: mcp run "vibe-check-mcp" --tool "analyze-tone" --args '{"text":"<content>"}'
  • When: Finalizing user-facing messages or documentation.

- adjust-response
  • Description: Rewrite content to match desired style/vibe.
  • Usage: mcp run "vibe-check-mcp" --tool "adjust-response" --args '{"text":"<content>","target":"<tone>"}'
  • When: Tailoring responses for formality or audience.


## Selection Heuristics
- Planning & decomposition: use sequential-think.plan or decompose.
- Bot actions (Discord events/messages): use discord-mcp tools.
- AI lookup or fact-check: use context7-mcp.fact-check or search-docs.
- Caching & rate control: use cache-mcp.get/set.
- Code scaffolding & edits: use code-mcp (covered in Common MCP Mappings).
- Review & quality checks: use code-review-mcp and vibe-check-mcp.
- Task management: use shrimp-task.create/list/complete.
- Tool discovery: use toolbox-mcp.discover-server.

## Invocation Pattern
mcp run "<server-id>" --tool "<tool>" --args '<JSON>' --output json

## Fallback
If no MCP server or tool matches the request, proceed with local logic and log the reason for audit.
