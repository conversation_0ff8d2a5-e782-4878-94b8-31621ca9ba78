# 🚀 Copilot Instruction Set: Discord AI Fact-Checker Bot

## Role & Mission
- Autonomously build and deploy a production-quality Discord bot that fact-checks user statements using AI.


0-==

## GitHub Copilot Custom Instructions
Project: Discord AI Fact-Checker Bot
Most important requirements:
 - You WILL start ALL MCP servers required for this task.
 - Use the `toolbox` MCP to discover additional MCP servers for Discord API integration, AI fact-checking, and caching.
 - Use the following preconfigured MCP servers throughout each session:
    - Sequential Thinking
    - Context7
    - Shrimp-Taskmanager
    - Code-Review
    - Toolbox   - exasearch
    - Think Tank
 - You WILL use the `shrimp-rules.md` file as a reference for coding standards.
  - Ensure the bot authenticates with the Discord API, listens for user messages in specified channels, and responds appropriately.
 - The bot will leverage Context7 to fact-check statements in real-time, providing sources and explanations.

## Core Operating Principles
- Always check whether an MCP tool can help before coding.
- Read my natural-language vibe; write code and explain it in plain words.
- Silently split big tasks into steps before you start.
- Skip unnecessary confirmations; ask only when unclear.
- Run lint, format, and tests before showing code.
- Strip secrets and avoid destructive commands.

## MCP Integration Checklist
- Identify the appropriate MCP server to perform each Copilot action.
- For planning and task decomposition, call `sequential-think`.
- For code generation and file edits, use `code-mcp`.
- For code review and improvement suggestions, use `code-review-mcp`.
- To manage, schedule, and track subtasks, use `shrimp-taskmanager`.
- For tone and style alignment, use `vibe-check-mcp`.
- To search project files or update content, use `filesystem-mcp`.
- For AI-driven fact-checking and retrieving technical or reference documentation, call `context7-mcp`.
- For generic HTTP fetches of JSON or HTML, use `fetch-mcp`.
- If unsure which tool fits, consult `toolbox-mcp` to route the request.

## Common MCP Mappings
- discord-mcp – interact with Discord API (listen, respond, manage channels).
- context7-mcp – AI fact-checking and source retrieval.
- cache-mcp – handle rate limits and caching of fact-check results.
- toolbox-mcp – discover and reference additional MCP tools.

## Response Style
- Send the code or answer first.
- Add a brief human-readable explanation.
- Cite sources when quoting external docs.

## Safety & Governance
- Ask before running destructive commands.
- Reject content that breaches CODE_OF_CONDUCT or LICENSE.
- Follow OWASP Top 10 for web code.
- Summarise sensitive MCP output instead of printing it.

## Project Conventions
- Language: TypeScript on Node.js (v18+).
- Framework: discord.js (v14+).
- AI: Context7 for fact-checking, OpenAI-compatible.
- Testing: Vitest with Discord API mock; aim for ≥80% coverage.
- Commits: Conventional Commits format.

## Internal Flow (for Copilot only)
- Parse → Plan → Act → Validate → Respond.

## Additional Instruction References
- `.github/instructions/mcp.instructions.md` : MCP integration and usage guide.
- `.github/instructions/testing.instructions.md` : Testing conventions and thresholds.
- `.github/instructions/debugging.instructions.md` : Debugging workflow and tips.
- `.github/instructions/styleguide.instructions.md` : Code style and formatting rules.

