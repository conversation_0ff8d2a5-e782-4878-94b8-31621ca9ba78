# 🧪 Testing Instructions: Discord AI Fact-Checker Bot

Goal – Maintain **≥80%** line and branch coverage; use TDD (Red → Green → Refactor).

When to Test
- Write a failing test first for each new command, event handler, or fact-check logic.
- Mock Discord events and Context7 API calls before implementing.
- Re-run tests on every commit and before merging.

Toolchain
- Vitest for unit & integration tests.
- discord.js mock utilities (e.g., `discord.js-mock` or custom stubs).
- MSW for mocking HTTP calls to Context7.
- c8 for coverage reporting.

Checklist
- Keep each Red → Green → Refactor cycle ≤5 min.
- Start with the smallest failing test (unit first, then integration).
- Use property-based tests for edge cases (e.g., unexpected message formats).
- Mock only external boundaries (Discord API, Context7).
- Refactor once all tests are green.

CI Gate
- `npm test -- --coverage` must exit 0 and enforce **80%** thresholds.

Reporting
- Coverage HTML output in `coverage/`; publish as CI artifact.
