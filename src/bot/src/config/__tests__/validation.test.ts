/**
 * Tests for Configuration Validation
 */

import { describe, it, expect } from 'vitest';
import { validateConfig, configSchema } from '../validation';
import { BotConfig, DEFAULT_CONFIG } from '@/types/config';

describe('Configuration Validation', () => {
  describe('validateConfig function', () => {
    it('should validate complete valid configuration', () => {
      const validConfig: BotConfig = {
        ...DEFAULT_CONFIG,
        discord: {
          ...DEFAULT_CONFIG.discord,
          token: 'valid-discord-token-with-minimum-50-characters-required',
          clientId: '123456789012345678'
        }
      };

      const result = validateConfig(validConfig);
      
      expect(result.error).toBeUndefined();
      expect(result.value).toBeDefined();
      expect(result.value?.discord.token).toBe(validConfig.discord.token);
    });

    it('should fail validation for missing required fields', () => {
      const invalidConfig = {
        discord: {
          // Missing token and clientId
          commandPrefix: '!'
        }
      };

      const result = validateConfig(invalidConfig);
      
      expect(result.error).toBeDefined();
      expect(result.error?.details).toBeDefined();
      expect(result.error?.details.length).toBeGreaterThan(0);
    });

    it('should fail validation for invalid field types', () => {
      const invalidConfig = {
        discord: {
          token: 'valid-discord-token-with-minimum-50-characters-required',
          clientId: '123456789012345678',
          autoFactCheck: 'not-a-boolean' // Should be boolean
        },
        hardware: {
          vramLimitGB: 'not-a-number' // Should be number
        }
      };

      const result = validateConfig(invalidConfig);
      
      expect(result.error).toBeDefined();
    });
  });

  describe('Discord Configuration Validation', () => {
    it('should validate valid Discord configuration', () => {
      const config = {
        discord: {
          token: 'valid-discord-token-with-minimum-50-characters-required',
          clientId: '123456789012345678',
          guildId: '987654321098765432',
          commandPrefix: '!',
          autoFactCheck: true,
          confidenceThreshold: 0.8,
          responseDelay: 3,
          maxResponseLength: 1500,
          sourceCount: 5
        }
      };

      const result = configSchema.validate(config);
      expect(result.error).toBeUndefined();
    });

    it('should fail for short Discord token', () => {
      const config = {
        discord: {
          token: 'short-token',
          clientId: '123456789012345678'
        }
      };

      const result = configSchema.validate(config);
      expect(result.error).toBeDefined();
      expect(result.error?.details[0].path).toContain('token');
    });

    it('should fail for invalid client ID format', () => {
      const config = {
        discord: {
          token: 'valid-discord-token-with-minimum-50-characters-required',
          clientId: 'not-a-number'
        }
      };

      const result = configSchema.validate(config);
      expect(result.error).toBeDefined();
      expect(result.error?.details[0].path).toContain('clientId');
    });

    it('should validate confidence threshold range', () => {
      const invalidConfigs = [
        { confidenceThreshold: -0.1 },
        { confidenceThreshold: 1.1 }
      ];

      invalidConfigs.forEach(invalidPart => {
        const config = {
          discord: {
            token: 'valid-discord-token-with-minimum-50-characters-required',
            clientId: '123456789012345678',
            ...invalidPart
          }
        };

        const result = configSchema.validate(config);
        expect(result.error).toBeDefined();
      });
    });
  });

  describe('Hardware Configuration Validation', () => {
    it('should validate valid hardware configuration', () => {
      const config = {
        hardware: {
          gpuAcceleration: true,
          vramLimitGB: 16,
          vramPreferredPercent: 50,
          vramMaxPercent: 70,
          cpuThreads: 12,
          gamingMode: true,
          autoDetectHardware: true
        }
      };

      const result = configSchema.validate(config);
      expect(result.error).toBeUndefined();
    });

    it('should fail when vramPreferredPercent >= vramMaxPercent', () => {
      const config = {
        hardware: {
          vramPreferredPercent: 80,
          vramMaxPercent: 70
        }
      };

      const result = configSchema.validate(config);
      expect(result.error).toBeDefined();
      expect(result.error?.message).toContain('vramPreferredPercent must be less than vramMaxPercent');
    });

    it('should validate VRAM limits', () => {
      const invalidConfigs = [
        { vramLimitGB: 0 },
        { vramLimitGB: 200 }
      ];

      invalidConfigs.forEach(invalidPart => {
        const config = {
          hardware: {
            ...invalidPart
          }
        };

        const result = configSchema.validate(config);
        expect(result.error).toBeDefined();
      });
    });
  });

  describe('Database Configuration Validation', () => {
    it('should validate SQLite configuration', () => {
      const config = {
        database: {
          type: 'sqlite',
          path: './data/bot.db',
          maxConnections: 5,
          connectionTimeout: 15000
        }
      };

      const result = configSchema.validate(config);
      expect(result.error).toBeUndefined();
    });

    it('should validate PostgreSQL configuration', () => {
      const config = {
        database: {
          type: 'postgresql',
          host: 'localhost',
          port: 5432,
          database: 'factbot',
          username: 'user',
          password: 'password',
          maxConnections: 20,
          connectionTimeout: 30000
        }
      };

      const result = configSchema.validate(config);
      expect(result.error).toBeUndefined();
    });

    it('should fail SQLite config with PostgreSQL fields', () => {
      const config = {
        database: {
          type: 'sqlite',
          path: './data/bot.db',
          host: 'localhost' // Should not be allowed for SQLite
        }
      };

      const result = configSchema.validate(config);
      expect(result.error).toBeDefined();
    });

    it('should fail PostgreSQL config without required fields', () => {
      const config = {
        database: {
          type: 'postgresql'
          // Missing host, database, username, password
        }
      };

      const result = configSchema.validate(config);
      expect(result.error).toBeDefined();
    });
  });

  describe('AI Configuration Validation', () => {
    it('should validate valid AI configuration', () => {
      const config = {
        ai: {
          modelPath: './models/qwen2.5-4b-instruct.gguf',
          modelName: 'qwen2.5-4b-instruct',
          contextLength: 32768,
          temperature: 0.7,
          topP: 0.9,
          topK: 40,
          repeatPenalty: 1.1,
          maxTokens: 2048,
          batchSize: 512,
          threads: 8,
          useGPU: true,
          gpuLayers: 35
        }
      };

      const result = configSchema.validate(config);
      expect(result.error).toBeUndefined();
    });

    it('should validate parameter ranges', () => {
      const invalidConfigs = [
        { temperature: -0.1 },
        { temperature: 2.1 },
        { topP: -0.1 },
        { topP: 1.1 },
        { contextLength: 100 },
        { contextLength: 200000 }
      ];

      invalidConfigs.forEach(invalidPart => {
        const config = {
          ai: {
            modelPath: './models/test.gguf',
            modelName: 'test',
            ...invalidPart
          }
        };

        const result = configSchema.validate(config);
        expect(result.error).toBeDefined();
      });
    });
  });

  describe('Web Crawler Configuration Validation', () => {
    it('should validate valid web crawler configuration', () => {
      const config = {
        webCrawler: {
          userAgent: 'Discord-FactChecker-Bot/1.0',
          requestDelay: 2000,
          maxConcurrentRequests: 3,
          timeout: 45000,
          maxRetries: 5,
          respectRobotsTxt: true,
          cacheEnabled: true,
          cacheTTL: 7200,
          maxCacheSize: 2000
        }
      };

      const result = configSchema.validate(config);
      expect(result.error).toBeUndefined();
    });

    it('should validate timing constraints', () => {
      const invalidConfigs = [
        { requestDelay: 50 }, // Too short
        { timeout: 1000 }, // Too short
        { cacheTTL: 30 } // Too short
      ];

      invalidConfigs.forEach(invalidPart => {
        const config = {
          webCrawler: {
            userAgent: 'Test Bot',
            ...invalidPart
          }
        };

        const result = configSchema.validate(config);
        expect(result.error).toBeDefined();
      });
    });
  });

  describe('Security Configuration Validation', () => {
    it('should validate valid security configuration', () => {
      const config = {
        security: {
          encryptionKey: 'a-very-long-encryption-key-with-32-plus-characters',
          rateLimiting: {
            enabled: true,
            windowMs: 120000,
            maxRequests: 20,
            skipSuccessfulRequests: true
          },
          trustedProxies: ['***********', '********'],
          allowedChannels: ['123456789', '987654321'],
          allowedUsers: ['111222333', '444555666'],
          blockedUsers: ['777888999'],
          adminUsers: ['123123123']
        }
      };

      const result = configSchema.validate(config);
      expect(result.error).toBeUndefined();
    });

    it('should validate IP addresses in trusted proxies', () => {
      const config = {
        security: {
          trustedProxies: ['invalid-ip', '***********']
        }
      };

      const result = configSchema.validate(config);
      expect(result.error).toBeDefined();
    });

    it('should validate Discord ID patterns', () => {
      const config = {
        security: {
          allowedChannels: ['not-a-number', '123456789']
        }
      };

      const result = configSchema.validate(config);
      expect(result.error).toBeDefined();
    });
  });

  describe('Default Values', () => {
    it('should apply default values for missing fields', () => {
      const minimalConfig = {
        discord: {
          token: 'valid-discord-token-with-minimum-50-characters-required',
          clientId: '123456789012345678'
        },
        hardware: {},
        logging: {}
      };

      const result = configSchema.validate(minimalConfig);

      expect(result.error).toBeUndefined();
      expect(result.value?.discord.commandPrefix).toBe('!');
      expect(result.value?.discord.autoFactCheck).toBe(true);
      expect(result.value?.hardware.gamingMode).toBe(true);
      expect(result.value?.logging.level).toBe('info');
    });
  });

  describe('Unknown Fields', () => {
    it('should strip unknown fields', () => {
      const configWithUnknown = {
        discord: {
          token: 'valid-discord-token-with-minimum-50-characters-required',
          clientId: '123456789012345678',
          unknownField: 'should-be-removed'
        },
        unknownSection: {
          someValue: 'should-be-removed'
        }
      };

      const result = configSchema.validate(configWithUnknown);
      
      expect(result.error).toBeUndefined();
      expect(result.value?.discord).not.toHaveProperty('unknownField');
      expect(result.value).not.toHaveProperty('unknownSection');
    });
  });
});
