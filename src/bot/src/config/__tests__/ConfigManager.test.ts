/**
 * Tests for Configuration Manager
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { existsSync, readFileSync, writeFileSync, unlinkSync, mkdirSync } from 'fs';
import { resolve } from 'path';
import { ConfigManager, getConfigManager, initializeConfig } from '../ConfigManager';
import { DEFAULT_CONFIG } from '@/types/config';

// Mock fs module
vi.mock('fs', () => ({
  existsSync: vi.fn(),
  readFileSync: vi.fn(),
  writeFileSync: vi.fn(),
  unlinkSync: vi.fn(),
  mkdirSync: vi.fn(),
  watchFile: vi.fn(),
  unwatchFile: vi.fn()
}));

const mockExistsSync = vi.mocked(existsSync);
const mockReadFileSync = vi.mocked(readFileSync);
const mockWriteFileSync = vi.mocked(writeFileSync);

describe('ConfigManager', () => {
  let configManager: ConfigManager;
  const testConfigPath = './test-config.yaml';

  beforeEach(() => {
    vi.clearAllMocks();
    // Reset environment variables
    delete process.env.FACTBOT_DISCORD_TOKEN;
    delete process.env.FACTBOT_VRAM_LIMIT_GB;
    delete process.env.FACTBOT_LOG_LEVEL;
  });

  afterEach(() => {
    if (configManager) {
      configManager.destroy();
    }
  });

  describe('Constructor and Initialization', () => {
    it('should create ConfigManager with default options', () => {
      mockExistsSync.mockReturnValue(false);

      configManager = new ConfigManager({ validateOnLoad: false });

      expect(configManager).toBeInstanceOf(ConfigManager);
      expect(configManager.getConfig()).toEqual(DEFAULT_CONFIG);
    });

    it('should create ConfigManager with custom options', () => {
      mockExistsSync.mockReturnValue(false);
      
      const options = {
        configPath: testConfigPath,
        watchForChanges: false,
        validateOnLoad: false
      };
      
      configManager = new ConfigManager(options);
      
      expect(configManager).toBeInstanceOf(ConfigManager);
    });

    it('should load default configuration when no config file exists', () => {
      mockExistsSync.mockReturnValue(false);

      configManager = new ConfigManager({ validateOnLoad: false });
      const config = configManager.getConfig();

      expect(config).toEqual(DEFAULT_CONFIG);
    });
  });

  describe('Configuration Loading', () => {
    it('should load configuration from YAML file', () => {
      const yamlConfig = `
discord:
  token: "test-token"
  clientId: "123456789"
  autoFactCheck: false
hardware:
  vramLimitGB: 16
  gamingMode: false
`;
      
      mockExistsSync.mockReturnValue(true);
      mockReadFileSync.mockReturnValue(yamlConfig);
      
      configManager = new ConfigManager({ configPath: 'test.yaml', validateOnLoad: false });
      const config = configManager.getConfig();
      
      expect(config.discord.token).toBe('test-token');
      expect(config.discord.clientId).toBe('123456789');
      expect(config.discord.autoFactCheck).toBe(false);
      expect(config.hardware.vramLimitGB).toBe(16);
      expect(config.hardware.gamingMode).toBe(false);
    });

    it('should load configuration from JSON file', () => {
      const jsonConfig = JSON.stringify({
        discord: {
          token: 'json-token',
          clientId: '987654321'
        },
        hardware: {
          vramLimitGB: 12
        }
      });
      
      mockExistsSync.mockReturnValue(true);
      mockReadFileSync.mockReturnValue(jsonConfig);
      
      configManager = new ConfigManager({ configPath: 'test.json', validateOnLoad: false });
      const config = configManager.getConfig();
      
      expect(config.discord.token).toBe('json-token');
      expect(config.discord.clientId).toBe('987654321');
      expect(config.hardware.vramLimitGB).toBe(12);
    });

    it('should throw error for unsupported file format', () => {
      mockExistsSync.mockReturnValue(true);
      mockReadFileSync.mockReturnValue('some content');
      
      expect(() => {
        configManager = new ConfigManager({ configPath: 'test.txt' });
      }).toThrow('Unsupported config file format: txt');
    });

    it('should throw error for invalid YAML', () => {
      const invalidYaml = 'invalid: yaml: content: [';
      
      mockExistsSync.mockReturnValue(true);
      mockReadFileSync.mockReturnValue(invalidYaml);
      
      expect(() => {
        configManager = new ConfigManager({ configPath: 'test.yaml' });
      }).toThrow();
    });
  });

  describe('Environment Variable Loading', () => {
    it('should load Discord configuration from environment variables', () => {
      process.env.FACTBOT_DISCORD_TOKEN = 'env-token';
      process.env.FACTBOT_DISCORD_CLIENT_ID = '111222333';
      process.env.FACTBOT_DISCORD_GUILD_ID = '444555666';
      
      mockExistsSync.mockReturnValue(false);
      
      configManager = new ConfigManager({ validateOnLoad: false });
      const config = configManager.getConfig();

      expect(config.discord.token).toBe('env-token');
      expect(config.discord.clientId).toBe('111222333');
      expect(config.discord.guildId).toBe('444555666');
    });

    it('should load hardware configuration from environment variables', () => {
      process.env.FACTBOT_VRAM_LIMIT_GB = '24';
      process.env.FACTBOT_GAMING_MODE = 'false';
      
      mockExistsSync.mockReturnValue(false);
      
      configManager = new ConfigManager({ validateOnLoad: false });
      const config = configManager.getConfig();

      expect(config.hardware.vramLimitGB).toBe(24);
      expect(config.hardware.gamingMode).toBe(false);
    });

    it('should load other configurations from environment variables', () => {
      process.env.FACTBOT_DATABASE_PATH = '/custom/db/path.db';
      process.env.FACTBOT_MODEL_PATH = '/custom/model/path.gguf';
      process.env.FACTBOT_LOG_LEVEL = 'debug';
      process.env.FACTBOT_DASHBOARD_PORT = '4000';
      
      mockExistsSync.mockReturnValue(false);
      
      configManager = new ConfigManager({ validateOnLoad: false });
      const config = configManager.getConfig();

      expect(config.database.path).toBe('/custom/db/path.db');
      expect(config.ai.modelPath).toBe('/custom/model/path.gguf');
      expect(config.logging.level).toBe('debug');
      expect(config.webDashboard.port).toBe(4000);
    });
  });

  describe('Configuration Merging', () => {
    it('should merge file and environment configurations correctly', () => {
      const yamlConfig = `
discord:
  token: "file-token"
  clientId: "123456789"
  autoFactCheck: true
hardware:
  vramLimitGB: 8
`;
      
      process.env.FACTBOT_DISCORD_TOKEN = 'env-token';
      process.env.FACTBOT_VRAM_LIMIT_GB = '16';
      
      mockExistsSync.mockReturnValue(true);
      mockReadFileSync.mockReturnValue(yamlConfig);
      
      configManager = new ConfigManager({ configPath: 'test.yaml', validateOnLoad: false });
      const config = configManager.getConfig();
      
      // Environment should override file
      expect(config.discord.token).toBe('env-token');
      expect(config.hardware.vramLimitGB).toBe(16);
      
      // File values should be preserved where no env override
      expect(config.discord.clientId).toBe('123456789');
      expect(config.discord.autoFactCheck).toBe(true);
    });

    it('should preserve nested object structure during merge', () => {
      const yamlConfig = `
discord:
  token: "test-token"
  autoFactCheck: false
logging:
  level: "debug"
  console:
    enabled: false
`;
      
      mockExistsSync.mockReturnValue(true);
      mockReadFileSync.mockReturnValue(yamlConfig);
      
      configManager = new ConfigManager({ configPath: 'test.yaml', validateOnLoad: false });
      const config = configManager.getConfig();
      
      expect(config.discord.token).toBe('test-token');
      expect(config.discord.autoFactCheck).toBe(false);
      expect(config.discord.clientId).toBe('123456789012345678'); // Default value preserved
      expect(config.logging.level).toBe('debug');
      expect(config.logging.console.enabled).toBe(false);
      expect(config.logging.console.colorize).toBe(true); // Default preserved
    });
  });

  describe('Configuration Access Methods', () => {
    beforeEach(() => {
      mockExistsSync.mockReturnValue(false);
      configManager = new ConfigManager({ validateOnLoad: false });
    });

    it('should get configuration value by path', () => {
      expect(configManager.get('discord.token')).toBe('default-discord-token-with-minimum-50-characters-required');
      expect(configManager.get('hardware.vramLimitGB')).toBe(8);
      expect(configManager.get('logging.level')).toBe('info');
    });

    it('should return undefined for non-existent paths', () => {
      expect(configManager.get('nonexistent.path')).toBeUndefined();
      expect(configManager.get('discord.nonexistent')).toBeUndefined();
    });

    it('should check if configuration has a specific path', () => {
      expect(configManager.has('discord.token')).toBe(true);
      expect(configManager.has('hardware.vramLimitGB')).toBe(true);
      expect(configManager.has('nonexistent.path')).toBe(false);
    });

    it('should return full configuration object', () => {
      const config = configManager.getConfig();
      expect(config).toEqual(DEFAULT_CONFIG);
      
      // Ensure it returns a copy, not reference
      config.discord.token = 'modified';
      expect(configManager.getConfig().discord.token).toBe('default-discord-token-with-minimum-50-characters-required');
    });
  });

  describe('Configuration Validation', () => {
    it('should validate valid configuration', () => {
      mockExistsSync.mockReturnValue(false);
      configManager = new ConfigManager({ validateOnLoad: false });

      const validation = configManager.validateConfiguration();
      expect(validation.isValid).toBe(true); // Default config now has valid values
      expect(validation.errors.length).toBe(0);
    });

    it('should validate configuration with required fields', () => {
      const validConfig = `
discord:
  token: "valid-token-with-minimum-50-characters-required-by-joi"
  clientId: "123456789012345678"
`;
      
      mockExistsSync.mockReturnValue(true);
      mockReadFileSync.mockReturnValue(validConfig);
      
      configManager = new ConfigManager({ 
        configPath: 'test.yaml',
        validateOnLoad: false // Don't validate on load to test manual validation
      });
      
      const validation = configManager.validateConfiguration();
      expect(validation.isValid).toBe(true);
      expect(validation.errors).toHaveLength(0);
    });

    it('should fail validation with invalid configuration', () => {
      const invalidConfig = `
discord:
  token: "short"
  clientId: "invalid"
hardware:
  vramLimitGB: -1
  vramPreferredPercent: 80
  vramMaxPercent: 70
`;
      
      mockExistsSync.mockReturnValue(true);
      mockReadFileSync.mockReturnValue(invalidConfig);
      
      expect(() => {
        configManager = new ConfigManager({ 
          configPath: 'test.yaml',
          validateOnLoad: true
        });
      }).toThrow('Configuration validation failed');
    });
  });

  describe('Global Configuration Manager', () => {
    it('should return same instance for getConfigManager', () => {
      mockExistsSync.mockReturnValue(false);
      
      const manager1 = getConfigManager({ validateOnLoad: false });
      const manager2 = getConfigManager({ validateOnLoad: false });
      
      expect(manager1).toBe(manager2);
      
      manager1.destroy();
    });

    it('should create new instance with initializeConfig', () => {
      mockExistsSync.mockReturnValue(false);
      
      const manager1 = getConfigManager({ validateOnLoad: false });
      const manager2 = initializeConfig({ configPath: 'new-config.yaml', validateOnLoad: false });
      
      expect(manager1).not.toBe(manager2);
      
      manager2.destroy();
    });
  });

  describe('Error Handling', () => {
    it('should throw error on file read error', () => {
      mockExistsSync.mockReturnValue(true);
      mockReadFileSync.mockImplementation(() => {
        throw new Error('File read error');
      });

      expect(() => {
        new ConfigManager({ configPath: 'test.yaml', validateOnLoad: false });
      }).toThrow('Failed to load config from test.yaml: Error: File read error');
    });

    it('should handle missing required environment variables gracefully', () => {
      mockExistsSync.mockReturnValue(false);
      
      // Don't set any environment variables
      configManager = new ConfigManager({ validateOnLoad: false });
      
      // Should not throw, should use defaults
      expect(() => configManager.getConfig()).not.toThrow();
    });
  });
});
