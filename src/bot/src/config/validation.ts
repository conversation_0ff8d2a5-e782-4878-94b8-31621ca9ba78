/**
 * Configuration validation schemas using Jo<PERSON>
 */

import <PERSON><PERSON> from 'joi';
import { BotConfig } from '@/types/config';

/**
 * Discord configuration validation schema
 */
const discordSchema = Joi.object({
  token: Joi.string().required().min(50).description('Discord bot token'),
  clientId: Joi.string().required().pattern(/^\d+$/).description('Discord client ID'),
  guildId: Joi.string().optional().pattern(/^\d+$/).description('Discord guild ID for development'),
  commandPrefix: Joi.string().default('!').max(5).description('Command prefix'),
  autoFactCheck: Joi.boolean().default(true).description('Enable automatic fact checking'),
  confidenceThreshold: Joi.number().min(0).max(1).default(0.7).description('Minimum confidence for responses'),
  responseDelay: Joi.number().min(0).max(60).default(2).description('Delay before responding (seconds)'),
  maxResponseLength: Joi.number().min(500).max(4000).default(2000).description('Maximum response length'),
  sourceCount: Joi.number().min(1).max(10).default(3).description('Number of sources to verify')
}).unknown(true).options({ stripUnknown: true });

/**
 * Hardware configuration validation schema
 */
const hardwareSchema = Joi.object({
  gpuAcceleration: Joi.boolean().default(true).description('Enable GPU acceleration'),
  vramLimitGB: Joi.number().min(1).max(128).default(8).description('VRAM limit in GB'),
  vramPreferredPercent: Joi.number().min(10).max(90).default(50).description('Preferred VRAM usage percentage'),
  vramMaxPercent: Joi.number().min(10).max(95).default(70).description('Maximum VRAM usage percentage'),
  cpuThreads: Joi.number().min(1).max(64).default(8).description('Number of CPU threads'),
  gamingMode: Joi.boolean().default(true).description('Enable gaming-friendly resource limits'),
  autoDetectHardware: Joi.boolean().default(true).description('Auto-detect hardware capabilities')
}).custom((value, helpers) => {
  if (value.vramPreferredPercent >= value.vramMaxPercent) {
    return helpers.error('hardware.vramPreferredPercent must be less than vramMaxPercent');
  }
  return value;
}).unknown(true).options({ stripUnknown: true });

/**
 * Database configuration validation schema
 */
const databaseSchema = Joi.object({
  type: Joi.string().valid('sqlite', 'postgresql').default('sqlite').description('Database type'),
  path: Joi.string().when('type', {
    is: 'sqlite',
    then: Joi.required(),
    otherwise: Joi.forbidden()
  }).description('SQLite database path'),
  host: Joi.string().when('type', {
    is: 'postgresql',
    then: Joi.required(),
    otherwise: Joi.forbidden()
  }).description('PostgreSQL host'),
  port: Joi.number().port().when('type', {
    is: 'postgresql',
    then: Joi.number().port().default(5432),
    otherwise: Joi.forbidden()
  }).description('PostgreSQL port'),
  database: Joi.string().when('type', {
    is: 'postgresql',
    then: Joi.required(),
    otherwise: Joi.forbidden()
  }).description('PostgreSQL database name'),
  username: Joi.string().when('type', {
    is: 'postgresql',
    then: Joi.required(),
    otherwise: Joi.forbidden()
  }).description('PostgreSQL username'),
  password: Joi.string().when('type', {
    is: 'postgresql',
    then: Joi.required(),
    otherwise: Joi.forbidden()
  }).description('PostgreSQL password'),
  maxConnections: Joi.number().min(1).max(100).default(10).description('Maximum database connections'),
  connectionTimeout: Joi.number().min(1000).max(300000).default(30000).description('Connection timeout (ms)')
}).unknown(true).options({ stripUnknown: true });

/**
 * AI configuration validation schema
 */
const aiSchema = Joi.object({
  modelPath: Joi.string().required().description('Path to AI model file'),
  modelName: Joi.string().required().description('AI model name'),
  contextLength: Joi.number().min(512).max(131072).default(32768).description('Context length'),
  temperature: Joi.number().min(0).max(2).default(0.7).description('Sampling temperature'),
  topP: Joi.number().min(0).max(1).default(0.9).description('Top-p sampling'),
  topK: Joi.number().min(1).max(200).default(40).description('Top-k sampling'),
  repeatPenalty: Joi.number().min(0.5).max(2).default(1.1).description('Repeat penalty'),
  maxTokens: Joi.number().min(128).max(8192).default(2048).description('Maximum tokens per response'),
  batchSize: Joi.number().min(1).max(2048).default(512).description('Batch size for processing'),
  threads: Joi.number().min(1).max(64).default(8).description('Number of threads for AI processing'),
  useGPU: Joi.boolean().default(true).description('Use GPU for AI inference'),
  gpuLayers: Joi.number().min(0).max(100).default(35).description('Number of GPU layers')
}).unknown(true).options({ stripUnknown: true });

/**
 * Web crawler configuration validation schema
 */
const webCrawlerSchema = Joi.object({
  userAgent: Joi.string().required().description('User agent string'),
  requestDelay: Joi.number().min(100).max(10000).default(1000).description('Delay between requests (ms)'),
  maxConcurrentRequests: Joi.number().min(1).max(20).default(5).description('Maximum concurrent requests'),
  timeout: Joi.number().min(5000).max(120000).default(30000).description('Request timeout (ms)'),
  maxRetries: Joi.number().min(0).max(10).default(3).description('Maximum retry attempts'),
  respectRobotsTxt: Joi.boolean().default(true).description('Respect robots.txt'),
  cacheEnabled: Joi.boolean().default(true).description('Enable response caching'),
  cacheTTL: Joi.number().min(60).max(86400).default(3600).description('Cache TTL (seconds)'),
  maxCacheSize: Joi.number().min(10).max(10000).default(1000).description('Maximum cache entries')
}).unknown(true).options({ stripUnknown: true });

/**
 * Web dashboard configuration validation schema
 */
const webDashboardSchema = Joi.object({
  enabled: Joi.boolean().default(true).description('Enable web dashboard'),
  port: Joi.number().port().default(3000).description('Dashboard port'),
  host: Joi.string().default('localhost').description('Dashboard host'),
  cors: Joi.object({
    enabled: Joi.boolean().default(true).description('Enable CORS'),
    origins: Joi.array().items(Joi.string().uri()).default(['http://localhost:3000']).description('Allowed origins')
  }),
  auth: Joi.object({
    enabled: Joi.boolean().default(false).description('Enable authentication'),
    username: Joi.string().when('enabled', {
      is: true,
      then: Joi.required(),
      otherwise: Joi.optional()
    }).description('Dashboard username'),
    password: Joi.string().min(8).when('enabled', {
      is: true,
      then: Joi.required(),
      otherwise: Joi.optional()
    }).description('Dashboard password'),
    sessionSecret: Joi.string().min(32).when('enabled', {
      is: true,
      then: Joi.required(),
      otherwise: Joi.optional()
    }).description('Session secret')
  }),
  ssl: Joi.object({
    enabled: Joi.boolean().default(false).description('Enable SSL'),
    keyPath: Joi.string().when('enabled', {
      is: true,
      then: Joi.required(),
      otherwise: Joi.optional()
    }).description('SSL key file path'),
    certPath: Joi.string().when('enabled', {
      is: true,
      then: Joi.required(),
      otherwise: Joi.optional()
    }).description('SSL certificate file path')
  })
}).unknown(true).options({ stripUnknown: true });

/**
 * Logging configuration validation schema
 */
const loggingSchema = Joi.object({
  level: Joi.string().valid('error', 'warn', 'info', 'debug', 'verbose').default('info').description('Log level'),
  console: Joi.object({
    enabled: Joi.boolean().default(true).description('Enable console logging'),
    colorize: Joi.boolean().default(true).description('Colorize console output'),
    timestamp: Joi.boolean().default(true).description('Include timestamps')
  }),
  file: Joi.object({
    enabled: Joi.boolean().default(true).description('Enable file logging'),
    path: Joi.string().default('./logs/bot.log').description('Log file path'),
    maxSize: Joi.string().pattern(/^\d+[kmg]?$/i).default('10m').description('Maximum log file size'),
    maxFiles: Joi.number().min(1).max(100).default(5).description('Maximum log files to keep'),
    rotateDaily: Joi.boolean().default(true).description('Rotate logs daily')
  }),
  database: Joi.object({
    enabled: Joi.boolean().default(true).description('Enable database logging'),
    tableName: Joi.string().default('logs').description('Log table name'),
    maxEntries: Joi.number().min(100).max(1000000).default(10000).description('Maximum log entries')
  })
}).unknown(true).options({ stripUnknown: true });

/**
 * Security configuration validation schema
 */
const securitySchema = Joi.object({
  encryptionKey: Joi.string().min(32).optional().description('Encryption key for sensitive data'),
  rateLimiting: Joi.object({
    enabled: Joi.boolean().default(true).description('Enable rate limiting'),
    windowMs: Joi.number().min(1000).max(3600000).default(60000).description('Rate limit window (ms)'),
    maxRequests: Joi.number().min(1).max(1000).default(10).description('Maximum requests per window'),
    skipSuccessfulRequests: Joi.boolean().default(false).description('Skip successful requests in rate limiting')
  }),
  trustedProxies: Joi.array().items(Joi.string().ip()).default([]).description('Trusted proxy IPs'),
  allowedChannels: Joi.array().items(Joi.string().pattern(/^\d+$/)).default([]).description('Allowed Discord channels'),
  allowedUsers: Joi.array().items(Joi.string().pattern(/^\d+$/)).default([]).description('Allowed Discord users'),
  blockedUsers: Joi.array().items(Joi.string().pattern(/^\d+$/)).default([]).description('Blocked Discord users'),
  adminUsers: Joi.array().items(Joi.string().pattern(/^\d+$/)).default([]).description('Admin Discord users')
}).unknown(true).options({ stripUnknown: true });

/**
 * Performance configuration validation schema
 */
const performanceSchema = Joi.object({
  caching: Joi.object({
    enabled: Joi.boolean().default(true).description('Enable caching'),
    ttl: Joi.number().min(60).max(86400).default(3600).description('Cache TTL (seconds)'),
    maxSize: Joi.number().min(10).max(100000).default(1000).description('Maximum cache entries'),
    checkPeriod: Joi.number().min(60).max(3600).default(600).description('Cache cleanup period (seconds)')
  }),
  monitoring: Joi.object({
    enabled: Joi.boolean().default(true).description('Enable monitoring'),
    metricsPort: Joi.number().port().default(9090).description('Metrics server port'),
    healthCheckInterval: Joi.number().min(5000).max(300000).default(30000).description('Health check interval (ms)')
  }),
  optimization: Joi.object({
    batchProcessing: Joi.boolean().default(true).description('Enable batch processing'),
    parallelProcessing: Joi.boolean().default(true).description('Enable parallel processing'),
    memoryLimit: Joi.number().min(512).max(16384).default(2048).description('Memory limit (MB)'),
    gcInterval: Joi.number().min(60000).max(3600000).default(300000).description('Garbage collection interval (ms)')
  })
}).unknown(true).options({ stripUnknown: true });

/**
 * Main configuration validation schema
 */
export const configSchema = Joi.object({
  discord: discordSchema.optional(),
  hardware: hardwareSchema.default(),
  database: databaseSchema.optional(),
  ai: aiSchema.optional(),
  webCrawler: webCrawlerSchema.optional(),
  webDashboard: webDashboardSchema.optional(),
  logging: loggingSchema.default(),
  security: securitySchema.optional(),
  performance: performanceSchema.optional()
}).unknown(true).options({ stripUnknown: true }).default();

/**
 * Full configuration validation schema (requires all sections)
 * Used for production configuration validation
 */
export const fullConfigSchema = Joi.object({
  discord: discordSchema.required(),
  hardware: hardwareSchema.required(),
  database: databaseSchema.required(),
  ai: aiSchema.required(),
  webCrawler: webCrawlerSchema.required(),
  webDashboard: webDashboardSchema.required(),
  logging: loggingSchema.required(),
  security: securitySchema.required(),
  performance: performanceSchema.required()
}).unknown(true).options({ stripUnknown: true });

/**
 * Validate configuration object
 */
export function validateConfig(config: Partial<BotConfig>): { error?: Error; value?: BotConfig } {
  const result = configSchema.validate(config, {
    allowUnknown: false,
    abortEarly: false,
    stripUnknown: true
  });

  if (result.error) {
    return { error: result.error };
  }

  return { value: result.value as BotConfig };
}
