/**
 * Configuration Manager for Discord AI Fact-Checker Bot
 * Handles loading, validation, and management of configuration from multiple sources
 */

import { readFileSync, existsSync, watchFile, unwatchFile } from 'fs';
import { resolve, dirname } from 'path';
import { EventEmitter } from 'events';
import YAML from 'yaml';
import { 
  BotConfig, 
  DEFAULT_CONFIG, 
  ConfigManagerOptions, 
  ConfigValidationResult,
  ConfigChangeEvent,
  ConfigSource,
  ConfigSourceInfo,
  EnvironmentConfig
} from '@/types/config';
import { validateConfig, fullConfigSchema } from './validation';

/**
 * Configuration Manager class
 * Provides centralized configuration management with validation and hot-reloading
 */
export class ConfigManager extends EventEmitter {
  private config: BotConfig;
  private configPath: string;
  private options: Required<ConfigManagerOptions>;
  private sourceInfo: Map<string, ConfigSourceInfo> = new Map();
  private isWatching: boolean = false;

  constructor(options: ConfigManagerOptions = {}) {
    super();
    
    // Set default options
    this.options = {
      configPath: options.configPath || './config/bot.yaml',
      watchForChanges: options.watchForChanges ?? true,
      validateOnLoad: options.validateOnLoad ?? true,
      allowPartialConfig: options.allowPartialConfig ?? false,
      environmentPrefix: options.environmentPrefix || 'FACTBOT_',
      defaultConfigPath: options.defaultConfigPath || './config/default.yaml'
    };

    this.configPath = resolve(this.options.configPath);
    this.config = { ...DEFAULT_CONFIG };
    
    this.loadConfiguration();
  }

  /**
   * Load configuration from all sources
   */
  private loadConfiguration(): void {
    try {
      // 1. Start with default configuration
      this.config = { ...DEFAULT_CONFIG };
      this.setSourceInfo('*', 'default');

      // 2. Load from default config file if exists
      if (existsSync(this.options.defaultConfigPath)) {
        const defaultFileConfig = this.loadFromFile(this.options.defaultConfigPath);
        this.mergeConfig(defaultFileConfig, 'file', this.options.defaultConfigPath);
      }

      // 3. Load from main config file
      if (existsSync(this.configPath)) {
        const fileConfig = this.loadFromFile(this.configPath);
        this.mergeConfig(fileConfig, 'file', this.configPath);
      }

      // 4. Load from environment variables
      const envConfig = this.loadFromEnvironment();
      if (Object.keys(envConfig).length > 0) {
        this.mergeConfig(envConfig, 'environment');
      }

      // 5. Validate final configuration
      if (this.options.validateOnLoad) {
        const validation = this.validateConfiguration();
        if (!validation.isValid) {
          const errorMessage = validation.errors.map(e => `${e.field}: ${e.message}`).join(', ');
          throw new Error(`Configuration validation failed: ${errorMessage}`);
        }
      }

      // 6. Start watching for changes
      if (this.options.watchForChanges && !this.isWatching) {
        this.startWatching();
      }

      this.emit('configLoaded', this.config);
    } catch (error) {
      this.emit('configError', error);
      throw error;
    }
  }

  /**
   * Load configuration from file
   */
  private loadFromFile(filePath: string): Partial<BotConfig> {
    try {
      const content = readFileSync(filePath, 'utf8');
      const ext = filePath.split('.').pop()?.toLowerCase();
      
      switch (ext) {
        case 'yaml':
        case 'yml':
          return YAML.parse(content);
        case 'json':
          return JSON.parse(content);
        default:
          throw new Error(`Unsupported config file format: ${ext}`);
      }
    } catch (error) {
      throw new Error(`Failed to load config from ${filePath}: ${error}`);
    }
  }

  /**
   * Load configuration from environment variables
   */
  private loadFromEnvironment(): Partial<BotConfig> {
    const envConfig: any = {};
    const prefix = this.options.environmentPrefix;

    // Discord configuration
    if (process.env[`${prefix}DISCORD_TOKEN`]) {
      envConfig.discord = {
        ...envConfig.discord,
        token: process.env[`${prefix}DISCORD_TOKEN`]
      };
    }
    if (process.env[`${prefix}DISCORD_CLIENT_ID`]) {
      envConfig.discord = {
        ...envConfig.discord,
        clientId: process.env[`${prefix}DISCORD_CLIENT_ID`]
      };
    }
    if (process.env[`${prefix}DISCORD_GUILD_ID`]) {
      envConfig.discord = {
        ...envConfig.discord,
        guildId: process.env[`${prefix}DISCORD_GUILD_ID`]
      };
    }

    // Hardware configuration
    if (process.env[`${prefix}VRAM_LIMIT_GB`]) {
      envConfig.hardware = {
        ...envConfig.hardware,
        vramLimitGB: parseInt(process.env[`${prefix}VRAM_LIMIT_GB`], 10)
      };
    }
    if (process.env[`${prefix}GAMING_MODE`]) {
      envConfig.hardware = {
        ...envConfig.hardware,
        gamingMode: process.env[`${prefix}GAMING_MODE`].toLowerCase() === 'true'
      };
    }

    // Database configuration
    if (process.env[`${prefix}DATABASE_PATH`]) {
      envConfig.database = {
        ...envConfig.database,
        path: process.env[`${prefix}DATABASE_PATH`]
      };
    }

    // AI configuration
    if (process.env[`${prefix}MODEL_PATH`]) {
      envConfig.ai = {
        ...envConfig.ai,
        modelPath: process.env[`${prefix}MODEL_PATH`]
      };
    }

    // Logging configuration
    if (process.env[`${prefix}LOG_LEVEL`]) {
      envConfig.logging = {
        ...envConfig.logging,
        level: process.env[`${prefix}LOG_LEVEL`]
      };
    }

    // Web dashboard configuration
    if (process.env[`${prefix}DASHBOARD_PORT`]) {
      envConfig.webDashboard = {
        ...envConfig.webDashboard,
        port: parseInt(process.env[`${prefix}DASHBOARD_PORT`], 10)
      };
    }

    return envConfig;
  }

  /**
   * Merge configuration from a source
   */
  private mergeConfig(
    sourceConfig: Partial<BotConfig>, 
    source: ConfigSource, 
    path?: string
  ): void {
    this.config = this.deepMerge(this.config, sourceConfig);
    this.setSourceInfo('*', source, path);
  }

  /**
   * Deep merge two configuration objects
   */
  private deepMerge(target: any, source: any): any {
    const result = { ...target };
    
    for (const key in source) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(target[key] || {}, source[key]);
      } else {
        result[key] = source[key];
      }
    }
    
    return result;
  }

  /**
   * Set source information for configuration tracking
   */
  private setSourceInfo(field: string, source: ConfigSource, path?: string): void {
    this.sourceInfo.set(field, {
      source,
      path,
      timestamp: new Date()
    });
  }

  /**
   * Start watching configuration files for changes
   */
  private startWatching(): void {
    if (this.isWatching) return;

    if (existsSync(this.configPath)) {
      watchFile(this.configPath, { interval: 1000 }, () => {
        this.handleConfigChange();
      });
    }

    this.isWatching = true;
  }

  /**
   * Stop watching configuration files
   */
  private stopWatching(): void {
    if (!this.isWatching) return;

    unwatchFile(this.configPath);
    this.isWatching = false;
  }

  /**
   * Handle configuration file changes
   */
  private handleConfigChange(): void {
    try {
      const oldConfig = { ...this.config };
      this.loadConfiguration();
      
      const changeEvent: ConfigChangeEvent = {
        field: 'config',
        oldValue: oldConfig,
        newValue: this.config,
        source: 'file',
        timestamp: new Date()
      };

      this.emit('configChanged', changeEvent);
    } catch (error) {
      this.emit('configError', error);
    }
  }

  /**
   * Validate current configuration
   */
  public validateConfiguration(useFullSchema: boolean = false): ConfigValidationResult {
    // Use validateConfig function for normal validation
    const result = validateConfig(this.config);

    if (result.error) {
      return {
        isValid: false,
        errors: result.error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message,
          value: detail.context?.value
        })),
        warnings: []
      };
    }

    return {
      isValid: true,
      errors: [],
      warnings: []
    };
  }

  /**
   * Get current configuration
   */
  public getConfig(): BotConfig {
    return { ...this.config };
  }

  /**
   * Get configuration value by path
   */
  public get<T = any>(path: string): T {
    const keys = path.split('.');
    let value: any = this.config;
    
    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        return undefined as T;
      }
    }
    
    return value as T;
  }

  /**
   * Check if configuration has a specific path
   */
  public has(path: string): boolean {
    return this.get(path) !== undefined;
  }

  /**
   * Get source information for a configuration field
   */
  public getSourceInfo(field: string): ConfigSourceInfo | undefined {
    return this.sourceInfo.get(field);
  }

  /**
   * Reload configuration from all sources
   */
  public reload(): void {
    this.loadConfiguration();
  }

  /**
   * Clean up resources
   */
  public destroy(): void {
    this.stopWatching();
    this.removeAllListeners();
  }
}

/**
 * Global configuration manager instance
 */
let globalConfigManager: ConfigManager | null = null;

/**
 * Get or create global configuration manager
 */
export function getConfigManager(options?: ConfigManagerOptions): ConfigManager {
  if (!globalConfigManager) {
    globalConfigManager = new ConfigManager(options);
  }
  return globalConfigManager;
}

/**
 * Initialize configuration manager with options
 */
export function initializeConfig(options?: ConfigManagerOptions): ConfigManager {
  if (globalConfigManager) {
    globalConfigManager.destroy();
  }
  globalConfigManager = new ConfigManager(options);
  return globalConfigManager;
}
