/**
 * Configuration type definitions for Discord AI Fact-Checker Bot
 */

export interface DiscordConfig {
  token: string;
  clientId: string;
  guildId?: string;
  commandPrefix: string;
  autoFactCheck: boolean;
  confidenceThreshold: number;
  responseDelay: number;
  maxResponseLength: number;
  sourceCount: number;
}

export interface HardwareConfig {
  gpuAcceleration: boolean;
  vramLimitGB: number;
  vramPreferredPercent: number;
  vramMaxPercent: number;
  cpuThreads: number;
  gamingMode: boolean;
  autoDetectHardware: boolean;
}

export interface DatabaseConfig {
  type: 'sqlite' | 'postgresql';
  path?: string; // For SQLite
  host?: string; // For PostgreSQL
  port?: number;
  database?: string;
  username?: string;
  password?: string;
  maxConnections: number;
  connectionTimeout: number;
}

export interface AIConfig {
  modelPath: string;
  modelName: string;
  contextLength: number;
  temperature: number;
  topP: number;
  topK: number;
  repeatPenalty: number;
  maxTokens: number;
  batchSize: number;
  threads: number;
  useGPU: boolean;
  gpuLayers: number;
}

export interface WebCrawlerConfig {
  userAgent: string;
  requestDelay: number;
  maxConcurrentRequests: number;
  timeout: number;
  maxRetries: number;
  respectRobotsTxt: boolean;
  cacheEnabled: boolean;
  cacheTTL: number;
  maxCacheSize: number;
}

export interface WebDashboardConfig {
  enabled: boolean;
  port: number;
  host: string;
  cors: {
    enabled: boolean;
    origins: string[];
  };
  auth: {
    enabled: boolean;
    username?: string;
    password?: string;
    sessionSecret?: string;
  };
  ssl: {
    enabled: boolean;
    keyPath?: string;
    certPath?: string;
  };
}

export interface LoggingConfig {
  level: 'error' | 'warn' | 'info' | 'debug' | 'verbose';
  console: {
    enabled: boolean;
    colorize: boolean;
    timestamp: boolean;
  };
  file: {
    enabled: boolean;
    path: string;
    maxSize: string;
    maxFiles: number;
    rotateDaily: boolean;
  };
  database: {
    enabled: boolean;
    tableName: string;
    maxEntries: number;
  };
}

export interface SecurityConfig {
  encryptionKey?: string;
  rateLimiting: {
    enabled: boolean;
    windowMs: number;
    maxRequests: number;
    skipSuccessfulRequests: boolean;
  };
  trustedProxies: string[];
  allowedChannels: string[];
  allowedUsers: string[];
  blockedUsers: string[];
  adminUsers: string[];
}

export interface PerformanceConfig {
  caching: {
    enabled: boolean;
    ttl: number;
    maxSize: number;
    checkPeriod: number;
  };
  monitoring: {
    enabled: boolean;
    metricsPort: number;
    healthCheckInterval: number;
  };
  optimization: {
    batchProcessing: boolean;
    parallelProcessing: boolean;
    memoryLimit: number;
    gcInterval: number;
  };
}

/**
 * Main configuration interface
 */
export interface BotConfig {
  discord: DiscordConfig;
  hardware: HardwareConfig;
  database: DatabaseConfig;
  ai: AIConfig;
  webCrawler: WebCrawlerConfig;
  webDashboard: WebDashboardConfig;
  logging: LoggingConfig;
  security: SecurityConfig;
  performance: PerformanceConfig;
}

/**
 * Environment-specific configuration
 */
export interface EnvironmentConfig {
  NODE_ENV: 'development' | 'production' | 'test';
  LOG_LEVEL: string;
  CONFIG_PATH: string;
  DATA_DIR: string;
  TEMP_DIR: string;
}

/**
 * Configuration validation schema types
 */
export interface ConfigValidationError {
  field: string;
  message: string;
  value?: any;
}

export interface ConfigValidationResult {
  isValid: boolean;
  errors: ConfigValidationError[];
  warnings: ConfigValidationError[];
}

/**
 * Configuration source types
 */
export type ConfigSource = 'file' | 'environment' | 'default' | 'override';

export interface ConfigSourceInfo {
  source: ConfigSource;
  path?: string;
  timestamp: Date;
}

/**
 * Configuration change event
 */
export interface ConfigChangeEvent {
  field: string;
  oldValue: any;
  newValue: any;
  source: ConfigSource;
  timestamp: Date;
}

/**
 * Configuration manager options
 */
export interface ConfigManagerOptions {
  configPath?: string;
  watchForChanges?: boolean;
  validateOnLoad?: boolean;
  allowPartialConfig?: boolean;
  environmentPrefix?: string;
  defaultConfigPath?: string;
}

/**
 * Default configuration values
 */
export const DEFAULT_CONFIG: BotConfig = {
  discord: {
    token: 'default-discord-token-with-minimum-50-characters-required',
    clientId: '123456789012345678',
    guildId: undefined,
    commandPrefix: '!',
    autoFactCheck: true,
    confidenceThreshold: 0.7,
    responseDelay: 2,
    maxResponseLength: 2000,
    sourceCount: 3
  },
  hardware: {
    gpuAcceleration: true,
    vramLimitGB: 8,
    vramPreferredPercent: 50,
    vramMaxPercent: 70,
    cpuThreads: 8,
    gamingMode: true,
    autoDetectHardware: true
  },
  database: {
    type: 'sqlite',
    path: './data/bot.db',
    maxConnections: 10,
    connectionTimeout: 30000
  },
  ai: {
    modelPath: './models/qwen2.5-4b-instruct.gguf',
    modelName: 'qwen2.5-4b-instruct',
    contextLength: 32768,
    temperature: 0.7,
    topP: 0.9,
    topK: 40,
    repeatPenalty: 1.1,
    maxTokens: 2048,
    batchSize: 512,
    threads: 8,
    useGPU: true,
    gpuLayers: 35
  },
  webCrawler: {
    userAgent: 'Discord-FactChecker-Bot/1.0',
    requestDelay: 1000,
    maxConcurrentRequests: 5,
    timeout: 30000,
    maxRetries: 3,
    respectRobotsTxt: true,
    cacheEnabled: true,
    cacheTTL: 3600,
    maxCacheSize: 1000
  },
  webDashboard: {
    enabled: true,
    port: 3000,
    host: 'localhost',
    cors: {
      enabled: true,
      origins: ['http://localhost:3000']
    },
    auth: {
      enabled: false
    },
    ssl: {
      enabled: false
    }
  },
  logging: {
    level: 'info',
    console: {
      enabled: true,
      colorize: true,
      timestamp: true
    },
    file: {
      enabled: true,
      path: './logs/bot.log',
      maxSize: '10m',
      maxFiles: 5,
      rotateDaily: true
    },
    database: {
      enabled: true,
      tableName: 'logs',
      maxEntries: 10000
    }
  },
  security: {
    rateLimiting: {
      enabled: true,
      windowMs: 60000,
      maxRequests: 10,
      skipSuccessfulRequests: false
    },
    trustedProxies: [],
    allowedChannels: [],
    allowedUsers: [],
    blockedUsers: [],
    adminUsers: []
  },
  performance: {
    caching: {
      enabled: true,
      ttl: 3600,
      maxSize: 1000,
      checkPeriod: 600
    },
    monitoring: {
      enabled: true,
      metricsPort: 9090,
      healthCheckInterval: 30000
    },
    optimization: {
      batchProcessing: true,
      parallelProcessing: true,
      memoryLimit: 2048,
      gcInterval: 300000
    }
  }
};
